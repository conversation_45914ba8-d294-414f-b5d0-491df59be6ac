---
title: >-
  3 must-watch talks on Legacy Code
date: 2021-12-12T12:48:00.000Z
description: "I have hosted more than 37 speakers to talk about Legacy Code tips and tricks. Here is my top 3 of the talks you should probably watch!"
image: /assets/remote-conf.jpg
tags:
  - conference
  - changing untested code
  - not sure where to start refactoring
  - software architecture
---

First of all, I received an interesting question this week:

> Just wondering, what is the story behind the name: The Legacy of SoCraTes? And also the last word's capitalization?

If you were wondering the same, I'm happy to solve the mystery for you now 😉

SoCraTes stands for **So**ftware **Cra**ft and **Tes**ting. The pun is intended. [It's a series of (un)conferences](https://www.socrates-conference.de/home) where people regroup to discuss software dev best practices.

The story behind "The Legacy of SoCraTes" edition is that I felt it was hard to find good talks on the topic of _Legacy Code_.

In March 2020, I was wrapping up the "SoCraTes Canada" conference, just before lockdown started in Canada (and around the world). Because the pandemic made the next IRL SoCraTes event very uncertain (spoiler: there was no edition in 2021). I decided to do something remotely. I reached out to <PERSON>, who also wanted to do something about Legacy Code, and we crafted "The Legacy of SoCraTes" from that.

The Legacy of SoCraTes is a remote conference. It's probably more like a meetup, actually. The event is free. I find 4-5 diverse speakers to share their expertise on a topic that touches Legacy Code and voilà!

As of today, we had 8 editions. We will probably do the next one in March 2022. Since you are following my updates, you will know about it before anyone else 😉

In the meantime, you can watch all previous editions [on the YouTube channel](https://www.youtube.com/c/TheLegacyofSoCraTes/videos).

## My top 3 🏆

As of December 2021, we have recorded 37 talks on the channel. That is overwhelming.

If you are not looking for a topic in particular but would like to learn useful things to work with Legacy Code, I've selected 3 talks for you!

### "Design patterns for modernizing legacy codebases" by Matthias Noback

[Matthias Noback](https://twitter.com/matthiasnoback) is one of my favorite sources of knowledge. His book [Advanced Web Application Architecture](https://matthiasnoback.nl/book/advanced-web-application-architecture/) is tremendously useful when working on the web.

I really like how Matthias approaches architecture from existing code. More often than not, I have to work with an imperfect, existing codebase. Having tools and techniques to refactor such a system is gold!

This talk is a sneak peek of the book. Discover a few design patterns that can really help you refactor existing code:

<iframe width="560" height="315" src="https://www.youtube-nocookie.com/embed/WI1QY6OMglE" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

### "Rewriting Legacy Code" by Anna Filina

​[Anna Filina](https://twitter.com/afilina) is an absolute expert in Legacy Code. I've had the pleasure to meet her at different conferences. She's an important source of inspiration for me.

In this talk, she raises the question: did you ever have to maintain a 15-year-old application? Dead code and tables everywhere, static methods, database queries in between HTML tags, and some pages still in PHP3 🙀

Anna leads you through a progressive rewrite from a very old legacy to the latest shiny version of PHP. She covers a bunch of useful topics such as:

- How to automate legacy testing
- How to seamlessly jump between the old and new parts
- How to overcome other challenges that arise from dealing with legacy

<iframe width="560" height="315" src="https://www.youtube-nocookie.com/embed/bTuvjjtGipY" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

### "How to Read Complex Code" by Dr Felienne Hermans

[Dr. Felienne Hermans](https://twitter.com/felienne) is the author of [The Programmer's Brain](https://www.felienne.com/book). She focuses on how your brain works, especially when working with unfamiliar code.

How to read code more efficiently? How to understand complex code? How to be more effective and empathic?

Felienne gives some insights in her talk. I really like how you can experiment it yourself with her little experiment.

Let's dive into Long-Term Memory, Short-Term Memory, and other brain concepts:

<iframe width="560" height="315" src="https://www.youtube-nocookie.com/embed/jjMlguOrWHc" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

If you never watched some of these talks, I recommend you schedule some time in your calendar to do so (~30min should do for each talk).

Of course, there is more! Many more that I didn't pick here, but which are great too, such as [Jim's talk on Hexagonal Architecture](https://www.youtube.com/watch?v=aayl6FysZ_U), [Emily explaining Approval Testing](https://www.youtube.com/watch?v=0ZVKcFsEp-4), [Adrianna presenting how Shopify refactors very large codebases](https://www.youtube.com/watch?v=zZ95_5y_iPk), etc.

To many more 🍻
