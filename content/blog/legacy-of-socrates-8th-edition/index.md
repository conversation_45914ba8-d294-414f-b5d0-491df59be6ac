---
title: "Testability, Empathy, and Safe Refactorings"
date: 2021-11-27T07:00:00.000Z
image: /assets/legacy-of-socrates-8th-edition.png
description: >-
  4 great talks on Legacy Code that I had the pleasure to host. This edition particularly focuses on testing and refactoring.
tags:
  - conference
  - changing untested code
  - not sure where to start refactoring
  - software architecture
---

On November 24, 2021, I co-organized the 8th edition of The Legacy of SoCraTes with my partner in crime [<PERSON>](https://twitter.com/adibolb).

It's a half-a-day remote conference where 4 speakers share their tips to deal with Legacy Code.

![The Legacy of SoCraTes](/assets/legacy-of-socrates-8th-edition.png)
![by <PERSON> and <PERSON>](/assets/legacy-of-socrates-organizers.png)

In this edition, we talked about testing a lot. Testing legacy code can be painful… but this pain can guide you towards a better design!

We also discussed attitudes in tech. While it's easy to make fun of "past developers who created this mess", it doesn't really help anyone to do so.

Finally, <PERSON> did a deep dive into refactoring gnarly code… when you don't have tests.

As usual, we recorded all the talks so you can watch and share them with your friends and colleagues!

## Engineering for Software: How to Amplify Creativity

[<PERSON>](https://twitter.com/davefarley77) is the author of [Continuous Delivery](https://martinfowler.com/books/continuousDelivery.html) and a thought leader in the field 👨‍🎤

I love how he regularly shares nuggets of golden advice [on his YouTube channel](https://www.youtube.com/channel/UCCfqyGl3nq_V0bo64CjZh8g). He covers a lot of topics… including working with Legacy Code!

In this talk, Dave digs into the tools software developers can use to do a better job: Testability and Speed.

<iframe width="560" height="315" src="https://www.youtube-nocookie.com/embed/fQ2qNTRvL4o" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

## Using tests pain as a design guide

[Barry O Sullivan](https://twitter.com/barryosull) is a PHP developer with a ton of experience dealing with Legacy Code. He believes it doesn't have to be painful… I agree!

This is better said than done. That's why in this talk, Barry dives into the usual problems you get when you start testing existing code:

- Long dependency bootstrapping chains
- Having to use partial mocks all around
- Dealing with magical injections of objects and data
- Seeding DB data everywhere
- In the end, **having a hard time reading tests code**!

<iframe width="560" height="315" src="https://www.youtube-nocookie.com/embed/mkQ-RvErLiU" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

Hopefully, this gives you concrete ideas to start improving your own tests!

## Let's Stop Making Each Other Feel Stupid

Have you ever heard colleagues making fun of someone who didn't know _the basic thing_ of software development?

I've been guilty of that myself ✋

The thing is: it doesn't really help anyone. Actually, it contributes to creating a culture where people don't feel safe sharing what they know or don't. Hopefully, we can do something about it: take this as an opportunity to teach!

[Clare Sudbery](https://twitter.com/ClareSudbery) is a lead engineer with Made Tech. She is also a maths graduate with 21 years of software experience. In this talk, she shares personal stories about gatekeeping in tech… and helpful advice to do better.

<iframe width="560" height="315" src="https://www.youtube-nocookie.com/embed/fNY6kFSM8hY" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

Relevant XKCD for this talk:

![Every day, ten thousands of US people hear something for the first time](https://imgs.xkcd.com/comics/ten_thousand_2x.png)

I also recommend [Clare's podcast: Making Tech Better](https://www.madetech.com/resources/podcasts/) where she hosts thoughts leaders of the industry to talk about the various topic of their expertise.

## How we use safe refactoring to untangle gnarly code without tests

Finally, I received [Jay Bazuzi](https://twitter.com/jaybazuzi).

In this talk, Jay answers the evergreen question:

> But wait… how would I refactor code without having tests?

Well, turns out there is a way. Jay uses Provable Refactorings, Arlo's Commit Notation, and leans on the tooling available to safely refactor code that's not covered with tests:

<iframe width="560" height="315" src="https://www.youtube-nocookie.com/embed/R1fHilWyUY4" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

## "These talks are really great! Can I have more?"

Sure thing!

There's no fixed schedule, but I like to organize this short conference every few months. I reach out to a few speakers to build an interesting agenda, then we set up the event.

Everything is FREE. Attending is the opportunity to directly ask questions to these speakers, from the comfort of your home.

If that's something you want to hear about, **subscribe to my newsletter** below 👇

I publish my monthly tips on Legacy Code here. I also foretell my subscribers about the conference agenda, so they don't miss out. Join them!
