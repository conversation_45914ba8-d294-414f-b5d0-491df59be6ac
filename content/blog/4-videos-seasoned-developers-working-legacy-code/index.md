---
title: 4 developers working their way through Legacy Code
date: 2020-09-01T21:13:12.338Z
image: /assets/acrobatic.jpg
description: >-
  Practice working with code you've never seen before helps make it easier. Here's my selection of videos that will expose you to seasoned developers doing that!
tags:
  - getting into a large codebase
  - changing untested code
---

> I'm wondering how my way of working through legacy code may be different from someone more seasoned…

Ooooh I feel you. That is a very, very common thought to have my friend 😃

In fact, most developers are only exposed to Legacy Code at work. You have to maintain an existing codebase because that's what is bringing money to the company. After all, [Legacy Code is _valuable_ code you're afraid to change](https://understandlegacycode.com/blog/what-is-legacy-code-is-it-code-without-tests#code-youre-not-comfortable-changing).

If you are lucky enough, you'll be working with seasoned developers who can teach you their tricks. But this is not very common.

So how do you know if you're doing it right? What can you do to make it easier to deal with Legacy Code?

Let me rephrase that: what can you do to make it easier to _perform_ working on Legacy Code?

**You practice.**

![Acrobats doing a pose](/assets/acrobatic.jpg)

Have you ever felt impressed by how acrobats make their moves look so easy, but you _know_ it's damn hard?

The thing is: performers spend most of their time practicing.

We, developers, spend most of our time performing. Little time is dedicated to actual practice. You know, trying some new moves, repeating them, perfecting them. Exercising our skills. Making it so when we are on the battlefield, we instinctively know what could work.

**Practicing is key in making Legacy Code easier to deal with.**

Let's briefly cover the few questions that are popping in your head:

- _Where to practice?_ On coding exercises that are designed for this. [Here's the list of 5 refactoring katas](https://understandlegacycode.com/blog/5-coding-exercises-to-practice-refactoring-legacy-code) I recommend.
- _When to practice?_ We don't all have the luxury to take on our personal time to do so. But your employer certainly has to train you every year. They have a budget to spend on this! Use it to develop skills that will help you in the long run 😉
- _What to practice?_ This is a very good question. Practicing is good, but it's better if someone can show you the moves…

## 4 developers who show you what to practice

When you're trying to learn new skills, it's a good idea to be looking for exposure to more experienced people on the topic.

If you're a visual learner, then it can be really helpful to _see_ someone practice. Personally, it helps me understand how to apply techniques in front of actual code. Theory is good, but I prefer when it gets in contact with reality.

It's not easy to find good resources online on that very topic.

To help you save some time (and get you into practicing), here's my selection of 4 resources that will expose you to seasoned developers working their way through Legacy Code!

### "Testing and Refactoring Legacy Code" from Sandro Mancuso

This one is in the "old, still gold" category 🏅

<iframe width="560" height="315" src="https://www.youtube-nocookie.com/embed/_NnElPO5BU0" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

It shows you useful refactoring moves on the Trip Service kata, dropping some tips doing so.

### A 6-parts series on refactoring Legacy Code, from Jovche Mitrejchevski

<iframe width="560" height="315" src="https://www.youtube-nocookie.com/embed/1ap11RbVGik" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

I really like this one because Jovche is demonstrating how to behave in front of a code you never saw before.

A lot of good refactoring techniques are presented in these videos, including different approaches to the same problem.

### A step-by-step refactoring in 2 parts, from Dave Farley

<iframe width="560" height="315" src="https://www.youtube-nocookie.com/embed/p-oWHEfXEVs" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

You may already know Dave Farley. He's the co-author of "[Continuous Delivery](https://martinfowler.com/books/continuousDelivery.html)". You can tell he's a very seasoned developer.

In these 2 videos, he shows how to address Legacy Code. Specifically:

1. Using [Approval Tests](../../approval-tests) to quickly cover existing code with tests
2. Removing clutter from the code with basic refactorings
3. Reducing complexity by extracting methods that got composed together

### Refactoring techniques, from Gregor Riegler

<iframe width="560" height="315" src="https://www.youtube-nocookie.com/embed/yqzYLm27cGw" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

Gregor has a few videos like this on his channel.

I like how they are straight to the point and digestible. It shows you a technique (e.g. Subclass & Override) that you can reproduce on a refactoring kata I mentioned previously.

## Looking for more techniques to rescue your codebase?

Dealing with Legacy Code in your daily job is hard because:

1. You don't have enough time to clean up everything
2. You don't want to make things worse

But what if you had a toolbox of techniques that will help you get your Legacy Code under control in no time?

Well, that's exactly what I'm working on at the moment! I'm bundling up the most useful and actionable tricks I know into the concrete guide I wish I had.

**Interested? Leave me your email** so I can tell you when it's out 👇
