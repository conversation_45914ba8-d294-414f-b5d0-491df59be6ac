<svg id="mermaid-1581420881872" width="644.140625" xmlns="http://www.w3.org/2000/svg" height="346.625" viewBox="0 0 644.140625 346.625"><style>



#mermaid-1581420881872 .label {
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family);
  color: #333; }

#mermaid-1581420881872 .label text {
  fill: #333; }

#mermaid-1581420881872 .node rect,
#mermaid-1581420881872 .node circle,
#mermaid-1581420881872 .node ellipse,
#mermaid-1581420881872 .node polygon,
#mermaid-1581420881872 .node path {
  fill: #ECECFF;
  stroke: #9370DB;
  stroke-width: 1px; }

#mermaid-1581420881872 .node .label {
  text-align: center; }

#mermaid-1581420881872 .node.clickable {
  cursor: pointer; }

#mermaid-1581420881872 .arrowheadPath {
  fill: #333333; }

#mermaid-1581420881872 .edgePath .path {
  stroke: #333333;
  stroke-width: 1.5px; }

#mermaid-1581420881872 .edgeLabel {
  background-color: #e8e8e8;
  text-align: center; }

#mermaid-1581420881872 .cluster rect {
  fill: #ffffde;
  stroke: #aaaa33;
  stroke-width: 1px; }

#mermaid-1581420881872 .cluster text {
  fill: #333; }

#mermaid-1581420881872 div.mermaidTooltip {
  position: absolute;
  text-align: center;
  max-width: 200px;
  padding: 2px;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family);
  font-size: 12px;
  background: #ffffde;
  border: 1px solid #aaaa33;
  border-radius: 2px;
  pointer-events: none;
  z-index: 100; }

#mermaid-1581420881872 .actor {
  stroke: #CCCCFF;
  fill: #ECECFF; }

#mermaid-1581420881872 text.actor {
  fill: black;
  stroke: none; }

#mermaid-1581420881872 .actor-line {
  stroke: grey; }

#mermaid-1581420881872 .messageLine0 {
  stroke-width: 1.5;
  stroke-dasharray: '2 2';
  stroke: #333; }

#mermaid-1581420881872 .messageLine1 {
  stroke-width: 1.5;
  stroke-dasharray: '2 2';
  stroke: #333; }

#mermaid-1581420881872 #arrowhead {
  fill: #333; }

#mermaid-1581420881872 .sequenceNumber {
  fill: white; }

#mermaid-1581420881872 #sequencenumber {
  fill: #333; }

#mermaid-1581420881872 #crosshead path {
  fill: #333 !important;
  stroke: #333 !important; }

#mermaid-1581420881872 .messageText {
  fill: #333;
  stroke: none; }

#mermaid-1581420881872 .labelBox {
  stroke: #CCCCFF;
  fill: #ECECFF; }

#mermaid-1581420881872 .labelText {
  fill: black;
  stroke: none; }

#mermaid-1581420881872 .loopText {
  fill: black;
  stroke: none; }

#mermaid-1581420881872 .loopLine {
  stroke-width: 2;
  stroke-dasharray: '2 2';
  stroke: #CCCCFF; }

#mermaid-1581420881872 .note {
  stroke: #aaaa33;
  fill: #fff5ad; }

#mermaid-1581420881872 .noteText {
  fill: black;
  stroke: none;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family);
  font-size: 14px; }

#mermaid-1581420881872 .activation0 {
  fill: #f4f4f4;
  stroke: #666; }

#mermaid-1581420881872 .activation1 {
  fill: #f4f4f4;
  stroke: #666; }

#mermaid-1581420881872 .activation2 {
  fill: #f4f4f4;
  stroke: #666; }


#mermaid-1581420881872 .mermaid-main-font {
  font-family: "trebuchet ms", verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581420881872 .section {
  stroke: none;
  opacity: 0.2; }

#mermaid-1581420881872 .section0 {
  fill: rgba(102, 102, 255, 0.49); }

#mermaid-1581420881872 .section2 {
  fill: #fff400; }

#mermaid-1581420881872 .section1,
#mermaid-1581420881872 .section3 {
  fill: white;
  opacity: 0.2; }

#mermaid-1581420881872 .sectionTitle0 {
  fill: #333; }

#mermaid-1581420881872 .sectionTitle1 {
  fill: #333; }

#mermaid-1581420881872 .sectionTitle2 {
  fill: #333; }

#mermaid-1581420881872 .sectionTitle3 {
  fill: #333; }

#mermaid-1581420881872 .sectionTitle {
  text-anchor: start;
  font-size: 11px;
  text-height: 14px;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }


#mermaid-1581420881872 .grid .tick {
  stroke: lightgrey;
  opacity: 0.8;
  shape-rendering: crispEdges; }
#mermaid-1581420881872   .grid .tick text {
    font-family: 'trebuchet ms', verdana, arial;
    font-family: var(--mermaid-font-family); }

#mermaid-1581420881872 .grid path {
  stroke-width: 0; }


#mermaid-1581420881872 .today {
  fill: none;
  stroke: red;
  stroke-width: 2px; }



#mermaid-1581420881872 .task {
  stroke-width: 2; }

#mermaid-1581420881872 .taskText {
  text-anchor: middle;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581420881872 .taskText:not([font-size]) {
  font-size: 11px; }

#mermaid-1581420881872 .taskTextOutsideRight {
  fill: black;
  text-anchor: start;
  font-size: 11px;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581420881872 .taskTextOutsideLeft {
  fill: black;
  text-anchor: end;
  font-size: 11px; }


#mermaid-1581420881872 .task.clickable {
  cursor: pointer; }

#mermaid-1581420881872 .taskText.clickable {
  cursor: pointer;
  fill: #003163 !important;
  font-weight: bold; }

#mermaid-1581420881872 .taskTextOutsideLeft.clickable {
  cursor: pointer;
  fill: #003163 !important;
  font-weight: bold; }

#mermaid-1581420881872 .taskTextOutsideRight.clickable {
  cursor: pointer;
  fill: #003163 !important;
  font-weight: bold; }


#mermaid-1581420881872 .taskText0,
#mermaid-1581420881872 .taskText1,
#mermaid-1581420881872 .taskText2,
#mermaid-1581420881872 .taskText3 {
  fill: white; }

#mermaid-1581420881872 .task0,
#mermaid-1581420881872 .task1,
#mermaid-1581420881872 .task2,
#mermaid-1581420881872 .task3 {
  fill: #8a90dd;
  stroke: #534fbc; }

#mermaid-1581420881872 .taskTextOutside0,
#mermaid-1581420881872 .taskTextOutside2 {
  fill: black; }

#mermaid-1581420881872 .taskTextOutside1,
#mermaid-1581420881872 .taskTextOutside3 {
  fill: black; }


#mermaid-1581420881872 .active0,
#mermaid-1581420881872 .active1,
#mermaid-1581420881872 .active2,
#mermaid-1581420881872 .active3 {
  fill: #bfc7ff;
  stroke: #534fbc; }

#mermaid-1581420881872 .activeText0,
#mermaid-1581420881872 .activeText1,
#mermaid-1581420881872 .activeText2,
#mermaid-1581420881872 .activeText3 {
  fill: black !important; }


#mermaid-1581420881872 .done0,
#mermaid-1581420881872 .done1,
#mermaid-1581420881872 .done2,
#mermaid-1581420881872 .done3 {
  stroke: grey;
  fill: lightgrey;
  stroke-width: 2; }

#mermaid-1581420881872 .doneText0,
#mermaid-1581420881872 .doneText1,
#mermaid-1581420881872 .doneText2,
#mermaid-1581420881872 .doneText3 {
  fill: black !important; }


#mermaid-1581420881872 .crit0,
#mermaid-1581420881872 .crit1,
#mermaid-1581420881872 .crit2,
#mermaid-1581420881872 .crit3 {
  stroke: #ff8888;
  fill: red;
  stroke-width: 2; }

#mermaid-1581420881872 .activeCrit0,
#mermaid-1581420881872 .activeCrit1,
#mermaid-1581420881872 .activeCrit2,
#mermaid-1581420881872 .activeCrit3 {
  stroke: #ff8888;
  fill: #bfc7ff;
  stroke-width: 2; }

#mermaid-1581420881872 .doneCrit0,
#mermaid-1581420881872 .doneCrit1,
#mermaid-1581420881872 .doneCrit2,
#mermaid-1581420881872 .doneCrit3 {
  stroke: #ff8888;
  fill: lightgrey;
  stroke-width: 2;
  cursor: pointer;
  shape-rendering: crispEdges; }

#mermaid-1581420881872 .milestone {
  transform: rotate(45deg) scale(0.8, 0.8); }

#mermaid-1581420881872 .milestoneText {
  font-style: italic; }

#mermaid-1581420881872 .doneCritText0,
#mermaid-1581420881872 .doneCritText1,
#mermaid-1581420881872 .doneCritText2,
#mermaid-1581420881872 .doneCritText3 {
  fill: black !important; }

#mermaid-1581420881872 .activeCritText0,
#mermaid-1581420881872 .activeCritText1,
#mermaid-1581420881872 .activeCritText2,
#mermaid-1581420881872 .activeCritText3 {
  fill: black !important; }

#mermaid-1581420881872 .titleText {
  text-anchor: middle;
  font-size: 18px;
  fill: black;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581420881872 g.classGroup text {
  fill: #9370DB;
  stroke: none;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family);
  font-size: 10px; }
#mermaid-1581420881872   g.classGroup text .title {
    font-weight: bolder; }

#mermaid-1581420881872 g.clickable {
  cursor: pointer; }

#mermaid-1581420881872 g.classGroup rect {
  fill: #ECECFF;
  stroke: #9370DB; }

#mermaid-1581420881872 g.classGroup line {
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581420881872 .classLabel .box {
  stroke: none;
  stroke-width: 0;
  fill: #ECECFF;
  opacity: 0.5; }

#mermaid-1581420881872 .classLabel .label {
  fill: #9370DB;
  font-size: 10px; }

#mermaid-1581420881872 .relation {
  stroke: #9370DB;
  stroke-width: 1;
  fill: none; }

#mermaid-1581420881872 .dashed-line {
  stroke-dasharray: 3; }

#mermaid-1581420881872 #compositionStart {
  fill: #9370DB;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581420881872 #compositionEnd {
  fill: #9370DB;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581420881872 #aggregationStart {
  fill: #ECECFF;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581420881872 #aggregationEnd {
  fill: #ECECFF;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581420881872 #dependencyStart {
  fill: #9370DB;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581420881872 #dependencyEnd {
  fill: #9370DB;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581420881872 #extensionStart {
  fill: #9370DB;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581420881872 #extensionEnd {
  fill: #9370DB;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581420881872 .commit-id,
#mermaid-1581420881872 .commit-msg,
#mermaid-1581420881872 .branch-label {
  fill: lightgrey;
  color: lightgrey;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581420881872 .pieTitleText {
  text-anchor: middle;
  font-size: 25px;
  fill: black;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581420881872 .slice {
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581420881872 g.stateGroup text {
  fill: #9370DB;
  stroke: none;
  font-size: 10px;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581420881872 g.stateGroup text {
  fill: #9370DB;
  stroke: none;
  font-size: 10px; }

#mermaid-1581420881872 g.stateGroup .state-title {
  font-weight: bolder;
  fill: black; }

#mermaid-1581420881872 g.stateGroup rect {
  fill: #ECECFF;
  stroke: #9370DB; }

#mermaid-1581420881872 g.stateGroup line {
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581420881872 .transition {
  stroke: #9370DB;
  stroke-width: 1;
  fill: none; }

#mermaid-1581420881872 .stateGroup .composit {
  fill: white;
  border-bottom: 1px; }

#mermaid-1581420881872 .stateGroup .alt-composit {
  fill: #e0e0e0;
  border-bottom: 1px; }

#mermaid-1581420881872 .state-note {
  stroke: #aaaa33;
  fill: #fff5ad; }
#mermaid-1581420881872   .state-note text {
    fill: black;
    stroke: none;
    font-size: 10px; }

#mermaid-1581420881872 .stateLabel .box {
  stroke: none;
  stroke-width: 0;
  fill: #ECECFF;
  opacity: 0.5; }

#mermaid-1581420881872 .stateLabel text {
  fill: black;
  font-size: 10px;
  font-weight: bold;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

:root {
  --mermaid-font-family: '"trebuchet ms", verdana, arial';
  --mermaid-font-family: "Comic Sans MS", "Comic Sans", cursive; }

:root { --mermaid-font-family: "trebuchet ms", verdana, arial;}</style><style>#mermaid-1581420881872 {
    color: rgb(0, 0, 0);
    font: 16px "trebuchet ms", verdana, arial;
  }</style><g transform="translate(0, 0)"><g class="output"><g class="clusters"><g class="cluster" id="booking" transform="translate(395.2109375,108.8125)" style="opacity: 1;"><rect width="481.859375" height="201.625" x="-240.9296875" y="-100.8125"></rect><g class="label" transform="translate(0, -86.8125)" id="mermaid-1581420881872Text"><g transform="translate(-27.75,-9.5)"><foreignObject width="55.5" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;">booking</div></foreignObject></g></g></g><g class="cluster" id="cart" style="opacity: 1;" transform="translate(510.6875,284.125)"><rect width="250.90625" height="109" x="-125.453125" y="-54.5"></rect><g class="label" transform="translate(0, -40.5)" id="mermaid-1581420881872Text"><g transform="translate(-14.4453125,-9.5)"><foreignObject width="28.890625" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;">cart</div></foreignObject></g></g></g></g><g class="edgePaths"><g class="edgePath" style="opacity: 1;"><path class="path" d="M70.671875,108.8125L112.4765625,108.8125L154.28125,108.8125L179.78125,109.3125" marker-end="url(#arrowhead6314)" style="fill:none"></path><defs><marker id="arrowhead6314" viewBox="0 0 10 10" refX="9" refY="5" markerUnits="strokeWidth" markerWidth="8" markerHeight="6" orient="auto"><path d="M 0 0 L 10 5 L 0 10 z" class="arrowheadPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker></defs></g><g class="edgePath" style="opacity: 1;"><path class="path" d="M294.47932692307694,126.23942307692307L348.0703125,144.46875L385.234375,144.46875L493.1706617811591,264.625" marker-end="url(#arrowhead6315)" style="fill:none"></path><defs><marker id="arrowhead6315" viewBox="0 0 10 10" refX="9" refY="5" markerUnits="strokeWidth" markerWidth="8" markerHeight="6" orient="auto"><path d="M 0 0 L 10 5 L 0 10 z" class="arrowheadPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker></defs></g><g class="edgePath" style="opacity: 1;"><path class="path" d="M299.32397994735214,97.23022994735211L348.0703125,85.65625L385.234375,85.65625L410.234375,85.65625" marker-end="url(#arrowhead6316)" style="fill:none"></path><defs><marker id="arrowhead6316" viewBox="0 0 10 10" refX="9" refY="5" markerUnits="strokeWidth" markerWidth="8" markerHeight="6" orient="auto"><path d="M 0 0 L 10 5 L 0 10 z" class="arrowheadPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker></defs></g></g><g class="edgeLabels"><g class="edgeLabel" transform="translate(112.4765625,108.8125)" style="opacity: 1;"><g transform="translate(-16.8046875,-9.5)" class="label"><foreignObject width="33.609375" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="edgeLabel">/pay</span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(348.0703125,144.46875)" style="opacity: 1;"><g transform="translate(-9.3984375,-9.5)" class="label"><foreignObject width="18.796875" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="edgeLabel">No</span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(348.0703125,85.65625)" style="opacity: 1;"><g transform="translate(-12.1640625,-9.5)" class="label"><foreignObject width="24.328125" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="edgeLabel">Yes</span></div></foreignObject></g></g></g><g class="nodes"><g class="node" id="C" transform="translate(510.6875,85.65625)" style="opacity: 1;"><rect rx="5" ry="5" x="-100.453125" y="-19.5" width="200.90625" height="39" class="label-container"></rect><g class="label" transform="translate(0,0)"><g transform="translate(-90.453125,-9.5)"><foreignObject width="180.90625" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;">handleRoundtripPayment</div></foreignObject></g></g></g><g class="node" style="opacity: 1;" id="D" transform="translate(245.09375,108.8125)"><polygon points="65.8125,0 131.625,-65.8125 65.8125,-131.625 0,-65.8125" transform="translate(-65.8125,65.8125)" class="label-container"></polygon><g class="label" transform="translate(0,0)"><g transform="translate(-43.625,-9.5)"><foreignObject width="87.25" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;">isRoundtrip?</div></foreignObject></g></g></g><g class="node" style="opacity: 1;" id="B" transform="translate(510.6875,284.125)"><rect rx="5" ry="5" x="-65.28125" y="-19.5" width="130.5625" height="39" class="label-container"></rect><g class="label" transform="translate(0,0)"><g transform="translate(-55.28125,-9.5)"><foreignObject width="110.5625" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;">handlePayment</div></foreignObject></g></g></g><g class="node" style="opacity: 1;" id="A" transform="translate(39.3359375,108.8125)"><rect rx="0" ry="0" x="-31.3359375" y="-19.5" width="62.671875" height="39" class="label-container"></rect><g class="label" transform="translate(0,0)"><g transform="translate(-21.3359375,-9.5)"><foreignObject width="42.671875" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;">Client</div></foreignObject></g></g></g></g></g></g></svg>