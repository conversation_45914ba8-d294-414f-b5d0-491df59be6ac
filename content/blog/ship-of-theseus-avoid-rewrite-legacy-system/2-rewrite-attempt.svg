<svg id="mermaid-1581472714193" width="621.6484375" xmlns="http://www.w3.org/2000/svg" height="254" viewBox="0 0 621.6484375 254"><style>



#mermaid-1581472714193 .label {
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family);
  color: #333; }

#mermaid-1581472714193 .label text {
  fill: #333; }

#mermaid-1581472714193 .node rect,
#mermaid-1581472714193 .node circle,
#mermaid-1581472714193 .node ellipse,
#mermaid-1581472714193 .node polygon,
#mermaid-1581472714193 .node path {
  fill: #ECECFF;
  stroke: #9370DB;
  stroke-width: 1px; }

#mermaid-1581472714193 .node .label {
  text-align: center; }

#mermaid-1581472714193 .node.clickable {
  cursor: pointer; }

#mermaid-1581472714193 .arrowheadPath {
  fill: #333333; }

#mermaid-1581472714193 .edgePath .path {
  stroke: #333333;
  stroke-width: 1.5px; }

#mermaid-1581472714193 .edgeLabel {
  background-color: #e8e8e8;
  text-align: center; }

#mermaid-1581472714193 .cluster rect {
  fill: #ffffde;
  stroke: #aaaa33;
  stroke-width: 1px; }

#mermaid-1581472714193 .cluster text {
  fill: #333; }

#mermaid-1581472714193 div.mermaidTooltip {
  position: absolute;
  text-align: center;
  max-width: 200px;
  padding: 2px;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family);
  font-size: 12px;
  background: #ffffde;
  border: 1px solid #aaaa33;
  border-radius: 2px;
  pointer-events: none;
  z-index: 100; }

#mermaid-1581472714193 .actor {
  stroke: #CCCCFF;
  fill: #ECECFF; }

#mermaid-1581472714193 text.actor {
  fill: black;
  stroke: none; }

#mermaid-1581472714193 .actor-line {
  stroke: grey; }

#mermaid-1581472714193 .messageLine0 {
  stroke-width: 1.5;
  stroke-dasharray: '2 2';
  stroke: #333; }

#mermaid-1581472714193 .messageLine1 {
  stroke-width: 1.5;
  stroke-dasharray: '2 2';
  stroke: #333; }

#mermaid-1581472714193 #arrowhead {
  fill: #333; }

#mermaid-1581472714193 .sequenceNumber {
  fill: white; }

#mermaid-1581472714193 #sequencenumber {
  fill: #333; }

#mermaid-1581472714193 #crosshead path {
  fill: #333 !important;
  stroke: #333 !important; }

#mermaid-1581472714193 .messageText {
  fill: #333;
  stroke: none; }

#mermaid-1581472714193 .labelBox {
  stroke: #CCCCFF;
  fill: #ECECFF; }

#mermaid-1581472714193 .labelText {
  fill: black;
  stroke: none; }

#mermaid-1581472714193 .loopText {
  fill: black;
  stroke: none; }

#mermaid-1581472714193 .loopLine {
  stroke-width: 2;
  stroke-dasharray: '2 2';
  stroke: #CCCCFF; }

#mermaid-1581472714193 .note {
  stroke: #aaaa33;
  fill: #fff5ad; }

#mermaid-1581472714193 .noteText {
  fill: black;
  stroke: none;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family);
  font-size: 14px; }

#mermaid-1581472714193 .activation0 {
  fill: #f4f4f4;
  stroke: #666; }

#mermaid-1581472714193 .activation1 {
  fill: #f4f4f4;
  stroke: #666; }

#mermaid-1581472714193 .activation2 {
  fill: #f4f4f4;
  stroke: #666; }


#mermaid-1581472714193 .mermaid-main-font {
  font-family: "trebuchet ms", verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581472714193 .section {
  stroke: none;
  opacity: 0.2; }

#mermaid-1581472714193 .section0 {
  fill: rgba(102, 102, 255, 0.49); }

#mermaid-1581472714193 .section2 {
  fill: #fff400; }

#mermaid-1581472714193 .section1,
#mermaid-1581472714193 .section3 {
  fill: white;
  opacity: 0.2; }

#mermaid-1581472714193 .sectionTitle0 {
  fill: #333; }

#mermaid-1581472714193 .sectionTitle1 {
  fill: #333; }

#mermaid-1581472714193 .sectionTitle2 {
  fill: #333; }

#mermaid-1581472714193 .sectionTitle3 {
  fill: #333; }

#mermaid-1581472714193 .sectionTitle {
  text-anchor: start;
  font-size: 11px;
  text-height: 14px;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }


#mermaid-1581472714193 .grid .tick {
  stroke: lightgrey;
  opacity: 0.8;
  shape-rendering: crispEdges; }
#mermaid-1581472714193   .grid .tick text {
    font-family: 'trebuchet ms', verdana, arial;
    font-family: var(--mermaid-font-family); }

#mermaid-1581472714193 .grid path {
  stroke-width: 0; }


#mermaid-1581472714193 .today {
  fill: none;
  stroke: red;
  stroke-width: 2px; }



#mermaid-1581472714193 .task {
  stroke-width: 2; }

#mermaid-1581472714193 .taskText {
  text-anchor: middle;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581472714193 .taskText:not([font-size]) {
  font-size: 11px; }

#mermaid-1581472714193 .taskTextOutsideRight {
  fill: black;
  text-anchor: start;
  font-size: 11px;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581472714193 .taskTextOutsideLeft {
  fill: black;
  text-anchor: end;
  font-size: 11px; }


#mermaid-1581472714193 .task.clickable {
  cursor: pointer; }

#mermaid-1581472714193 .taskText.clickable {
  cursor: pointer;
  fill: #003163 !important;
  font-weight: bold; }

#mermaid-1581472714193 .taskTextOutsideLeft.clickable {
  cursor: pointer;
  fill: #003163 !important;
  font-weight: bold; }

#mermaid-1581472714193 .taskTextOutsideRight.clickable {
  cursor: pointer;
  fill: #003163 !important;
  font-weight: bold; }


#mermaid-1581472714193 .taskText0,
#mermaid-1581472714193 .taskText1,
#mermaid-1581472714193 .taskText2,
#mermaid-1581472714193 .taskText3 {
  fill: white; }

#mermaid-1581472714193 .task0,
#mermaid-1581472714193 .task1,
#mermaid-1581472714193 .task2,
#mermaid-1581472714193 .task3 {
  fill: #8a90dd;
  stroke: #534fbc; }

#mermaid-1581472714193 .taskTextOutside0,
#mermaid-1581472714193 .taskTextOutside2 {
  fill: black; }

#mermaid-1581472714193 .taskTextOutside1,
#mermaid-1581472714193 .taskTextOutside3 {
  fill: black; }


#mermaid-1581472714193 .active0,
#mermaid-1581472714193 .active1,
#mermaid-1581472714193 .active2,
#mermaid-1581472714193 .active3 {
  fill: #bfc7ff;
  stroke: #534fbc; }

#mermaid-1581472714193 .activeText0,
#mermaid-1581472714193 .activeText1,
#mermaid-1581472714193 .activeText2,
#mermaid-1581472714193 .activeText3 {
  fill: black !important; }


#mermaid-1581472714193 .done0,
#mermaid-1581472714193 .done1,
#mermaid-1581472714193 .done2,
#mermaid-1581472714193 .done3 {
  stroke: grey;
  fill: lightgrey;
  stroke-width: 2; }

#mermaid-1581472714193 .doneText0,
#mermaid-1581472714193 .doneText1,
#mermaid-1581472714193 .doneText2,
#mermaid-1581472714193 .doneText3 {
  fill: black !important; }


#mermaid-1581472714193 .crit0,
#mermaid-1581472714193 .crit1,
#mermaid-1581472714193 .crit2,
#mermaid-1581472714193 .crit3 {
  stroke: #ff8888;
  fill: red;
  stroke-width: 2; }

#mermaid-1581472714193 .activeCrit0,
#mermaid-1581472714193 .activeCrit1,
#mermaid-1581472714193 .activeCrit2,
#mermaid-1581472714193 .activeCrit3 {
  stroke: #ff8888;
  fill: #bfc7ff;
  stroke-width: 2; }

#mermaid-1581472714193 .doneCrit0,
#mermaid-1581472714193 .doneCrit1,
#mermaid-1581472714193 .doneCrit2,
#mermaid-1581472714193 .doneCrit3 {
  stroke: #ff8888;
  fill: lightgrey;
  stroke-width: 2;
  cursor: pointer;
  shape-rendering: crispEdges; }

#mermaid-1581472714193 .milestone {
  transform: rotate(45deg) scale(0.8, 0.8); }

#mermaid-1581472714193 .milestoneText {
  font-style: italic; }

#mermaid-1581472714193 .doneCritText0,
#mermaid-1581472714193 .doneCritText1,
#mermaid-1581472714193 .doneCritText2,
#mermaid-1581472714193 .doneCritText3 {
  fill: black !important; }

#mermaid-1581472714193 .activeCritText0,
#mermaid-1581472714193 .activeCritText1,
#mermaid-1581472714193 .activeCritText2,
#mermaid-1581472714193 .activeCritText3 {
  fill: black !important; }

#mermaid-1581472714193 .titleText {
  text-anchor: middle;
  font-size: 18px;
  fill: black;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581472714193 g.classGroup text {
  fill: #9370DB;
  stroke: none;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family);
  font-size: 10px; }
#mermaid-1581472714193   g.classGroup text .title {
    font-weight: bolder; }

#mermaid-1581472714193 g.clickable {
  cursor: pointer; }

#mermaid-1581472714193 g.classGroup rect {
  fill: #ECECFF;
  stroke: #9370DB; }

#mermaid-1581472714193 g.classGroup line {
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581472714193 .classLabel .box {
  stroke: none;
  stroke-width: 0;
  fill: #ECECFF;
  opacity: 0.5; }

#mermaid-1581472714193 .classLabel .label {
  fill: #9370DB;
  font-size: 10px; }

#mermaid-1581472714193 .relation {
  stroke: #9370DB;
  stroke-width: 1;
  fill: none; }

#mermaid-1581472714193 .dashed-line {
  stroke-dasharray: 3; }

#mermaid-1581472714193 #compositionStart {
  fill: #9370DB;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581472714193 #compositionEnd {
  fill: #9370DB;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581472714193 #aggregationStart {
  fill: #ECECFF;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581472714193 #aggregationEnd {
  fill: #ECECFF;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581472714193 #dependencyStart {
  fill: #9370DB;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581472714193 #dependencyEnd {
  fill: #9370DB;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581472714193 #extensionStart {
  fill: #9370DB;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581472714193 #extensionEnd {
  fill: #9370DB;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581472714193 .commit-id,
#mermaid-1581472714193 .commit-msg,
#mermaid-1581472714193 .branch-label {
  fill: lightgrey;
  color: lightgrey;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581472714193 .pieTitleText {
  text-anchor: middle;
  font-size: 25px;
  fill: black;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581472714193 .slice {
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581472714193 g.stateGroup text {
  fill: #9370DB;
  stroke: none;
  font-size: 10px;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581472714193 g.stateGroup text {
  fill: #9370DB;
  stroke: none;
  font-size: 10px; }

#mermaid-1581472714193 g.stateGroup .state-title {
  font-weight: bolder;
  fill: black; }

#mermaid-1581472714193 g.stateGroup rect {
  fill: #ECECFF;
  stroke: #9370DB; }

#mermaid-1581472714193 g.stateGroup line {
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581472714193 .transition {
  stroke: #9370DB;
  stroke-width: 1;
  fill: none; }

#mermaid-1581472714193 .stateGroup .composit {
  fill: white;
  border-bottom: 1px; }

#mermaid-1581472714193 .stateGroup .alt-composit {
  fill: #e0e0e0;
  border-bottom: 1px; }

#mermaid-1581472714193 .state-note {
  stroke: #aaaa33;
  fill: #fff5ad; }
#mermaid-1581472714193   .state-note text {
    fill: black;
    stroke: none;
    font-size: 10px; }

#mermaid-1581472714193 .stateLabel .box {
  stroke: none;
  stroke-width: 0;
  fill: #ECECFF;
  opacity: 0.5; }

#mermaid-1581472714193 .stateLabel text {
  fill: black;
  font-size: 10px;
  font-weight: bold;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

:root {
  --mermaid-font-family: '"trebuchet ms", verdana, arial';
  --mermaid-font-family: "Comic Sans MS", "Comic Sans", cursive; }

:root { --mermaid-font-family: "trebuchet ms", verdana, arial;}</style><style>#mermaid-1581472714193 {
    color: rgb(0, 0, 0);
    font: 16px "trebuchet ms", verdana, arial;
  }</style><g transform="translate(0, 0)"><g class="output"><g class="clusters"><g class="cluster" id="booking" transform="translate(523.3671875,62.5)" style="opacity: 1;"><rect width="180.5625" height="109" x="-90.28125" y="-54.5"></rect><g class="label" transform="translate(0, -40.5)" id="mermaid-1581472714193Text"><g transform="translate(-27.75,-9.5)"><foreignObject width="55.5" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;">booking</div></foreignObject></g></g></g><g class="cluster" id="cart" style="opacity: 1;" transform="translate(523.3671875,191.5)"><rect width="180.5625" height="109" x="-90.28125" y="-54.5"></rect><g class="label" transform="translate(0, -40.5)" id="mermaid-1581472714193Text"><g transform="translate(-14.4453125,-9.5)"><foreignObject width="28.890625" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;">cart</div></foreignObject></g></g></g></g><g class="edgePaths"><g class="edgePath" style="opacity: 1;"><path class="path" d="M70.671875,127L95.671875,127L121.171875,127.5" marker-end="url(#arrowhead8459)" style="fill:none"></path><defs><marker id="arrowhead8459" viewBox="0 0 10 10" refX="9" refY="5" markerUnits="strokeWidth" markerWidth="8" markerHeight="6" orient="auto"><path d="M 0 0 L 10 5 L 0 10 z" class="arrowheadPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker></defs></g><g class="edgePath" style="opacity: 1;"><path class="path" d="M232.18277594091256,146.49691155908744L341.8828125,191.5L433.0859375,191.5L458.0859375,191.5" marker-end="url(#arrowhead8460)" style="fill:none"></path><defs><marker id="arrowhead8460" viewBox="0 0 10 10" refX="9" refY="5" markerUnits="strokeWidth" markerWidth="8" markerHeight="6" orient="auto"><path d="M 0 0 L 10 5 L 0 10 z" class="arrowheadPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker></defs></g><g class="edgePath" style="opacity: 1;"><path class="path" d="M232.18277594091256,108.50308844091255L341.8828125,62.5L433.0859375,62.5L458.0859375,62.5" marker-end="url(#arrowhead8461)" style="fill:none"></path><defs><marker id="arrowhead8461" viewBox="0 0 10 10" refX="9" refY="5" markerUnits="strokeWidth" markerWidth="8" markerHeight="6" orient="auto"><path d="M 0 0 L 10 5 L 0 10 z" class="arrowheadPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker></defs></g></g><g class="edgeLabels"><g class="edgeLabel" transform="" style="opacity: 1;"><g transform="translate(0,0)" class="label"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(341.8828125,191.5)" style="opacity: 1;"><g transform="translate(-52.8984375,-9.5)" class="label"><foreignObject width="105.796875" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="edgeLabel">/pay-with-cart</span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(341.8828125,62.5)" style="opacity: 1;"><g transform="translate(-66.203125,-9.5)" class="label"><foreignObject width="132.40625" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="edgeLabel">/pay-with-booking</span></div></foreignObject></g></g></g><g class="nodes"><g class="node" id="C" transform="translate(523.3671875,62.5)" style="opacity: 1;"><rect rx="5" ry="5" x="-65.28125" y="-19.5" width="130.5625" height="39" class="label-container"></rect><g class="label" transform="translate(0,0)"><g transform="translate(-55.28125,-9.5)"><foreignObject width="110.5625" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;">handlePayment</div></foreignObject></g></g></g><g class="node" style="opacity: 1;" id="B" transform="translate(523.3671875,191.5)"><rect rx="5" ry="5" x="-65.28125" y="-19.5" width="130.5625" height="39" class="label-container"></rect><g class="label" transform="translate(0,0)"><g transform="translate(-55.28125,-9.5)"><foreignObject width="110.5625" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;">handlePayment</div></foreignObject></g></g></g><g class="node" style="opacity: 1;" id="A" transform="translate(39.3359375,127)"><rect rx="0" ry="0" x="-31.3359375" y="-19.5" width="62.671875" height="39" class="label-container"></rect><g class="label" transform="translate(0,0)"><g transform="translate(-21.3359375,-9.5)"><foreignObject width="42.671875" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;">Client</div></foreignObject></g></g></g><g class="node" style="opacity: 1;" id="D" transform="translate(185.67578125,127)"><polygon points="65.00390625,0 130.0078125,-65.00390625 65.00390625,-130.0078125 0,-65.00390625" transform="translate(-65.00390625,65.00390625)" class="label-container"></polygon><g class="label" transform="translate(0,0)"><g transform="translate(-42.7265625,-9.5)"><foreignObject width="85.453125" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;">useBooking?</div></foreignObject></g></g></g></g></g></g></svg>