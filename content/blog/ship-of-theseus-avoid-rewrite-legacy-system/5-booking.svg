<svg id="mermaid-1581472930770" width="342.84375" xmlns="http://www.w3.org/2000/svg" height="125" viewBox="0 0 342.84375 125"><style>



#mermaid-1581472930770 .label {
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family);
  color: #333; }

#mermaid-1581472930770 .label text {
  fill: #333; }

#mermaid-1581472930770 .node rect,
#mermaid-1581472930770 .node circle,
#mermaid-1581472930770 .node ellipse,
#mermaid-1581472930770 .node polygon,
#mermaid-1581472930770 .node path {
  fill: #ECECFF;
  stroke: #9370DB;
  stroke-width: 1px; }

#mermaid-1581472930770 .node .label {
  text-align: center; }

#mermaid-1581472930770 .node.clickable {
  cursor: pointer; }

#mermaid-1581472930770 .arrowheadPath {
  fill: #333333; }

#mermaid-1581472930770 .edgePath .path {
  stroke: #333333;
  stroke-width: 1.5px; }

#mermaid-1581472930770 .edgeLabel {
  background-color: #e8e8e8;
  text-align: center; }

#mermaid-1581472930770 .cluster rect {
  fill: #ffffde;
  stroke: #aaaa33;
  stroke-width: 1px; }

#mermaid-1581472930770 .cluster text {
  fill: #333; }

#mermaid-1581472930770 div.mermaidTooltip {
  position: absolute;
  text-align: center;
  max-width: 200px;
  padding: 2px;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family);
  font-size: 12px;
  background: #ffffde;
  border: 1px solid #aaaa33;
  border-radius: 2px;
  pointer-events: none;
  z-index: 100; }

#mermaid-1581472930770 .actor {
  stroke: #CCCCFF;
  fill: #ECECFF; }

#mermaid-1581472930770 text.actor {
  fill: black;
  stroke: none; }

#mermaid-1581472930770 .actor-line {
  stroke: grey; }

#mermaid-1581472930770 .messageLine0 {
  stroke-width: 1.5;
  stroke-dasharray: '2 2';
  stroke: #333; }

#mermaid-1581472930770 .messageLine1 {
  stroke-width: 1.5;
  stroke-dasharray: '2 2';
  stroke: #333; }

#mermaid-1581472930770 #arrowhead {
  fill: #333; }

#mermaid-1581472930770 .sequenceNumber {
  fill: white; }

#mermaid-1581472930770 #sequencenumber {
  fill: #333; }

#mermaid-1581472930770 #crosshead path {
  fill: #333 !important;
  stroke: #333 !important; }

#mermaid-1581472930770 .messageText {
  fill: #333;
  stroke: none; }

#mermaid-1581472930770 .labelBox {
  stroke: #CCCCFF;
  fill: #ECECFF; }

#mermaid-1581472930770 .labelText {
  fill: black;
  stroke: none; }

#mermaid-1581472930770 .loopText {
  fill: black;
  stroke: none; }

#mermaid-1581472930770 .loopLine {
  stroke-width: 2;
  stroke-dasharray: '2 2';
  stroke: #CCCCFF; }

#mermaid-1581472930770 .note {
  stroke: #aaaa33;
  fill: #fff5ad; }

#mermaid-1581472930770 .noteText {
  fill: black;
  stroke: none;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family);
  font-size: 14px; }

#mermaid-1581472930770 .activation0 {
  fill: #f4f4f4;
  stroke: #666; }

#mermaid-1581472930770 .activation1 {
  fill: #f4f4f4;
  stroke: #666; }

#mermaid-1581472930770 .activation2 {
  fill: #f4f4f4;
  stroke: #666; }


#mermaid-1581472930770 .mermaid-main-font {
  font-family: "trebuchet ms", verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581472930770 .section {
  stroke: none;
  opacity: 0.2; }

#mermaid-1581472930770 .section0 {
  fill: rgba(102, 102, 255, 0.49); }

#mermaid-1581472930770 .section2 {
  fill: #fff400; }

#mermaid-1581472930770 .section1,
#mermaid-1581472930770 .section3 {
  fill: white;
  opacity: 0.2; }

#mermaid-1581472930770 .sectionTitle0 {
  fill: #333; }

#mermaid-1581472930770 .sectionTitle1 {
  fill: #333; }

#mermaid-1581472930770 .sectionTitle2 {
  fill: #333; }

#mermaid-1581472930770 .sectionTitle3 {
  fill: #333; }

#mermaid-1581472930770 .sectionTitle {
  text-anchor: start;
  font-size: 11px;
  text-height: 14px;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }


#mermaid-1581472930770 .grid .tick {
  stroke: lightgrey;
  opacity: 0.8;
  shape-rendering: crispEdges; }
#mermaid-1581472930770   .grid .tick text {
    font-family: 'trebuchet ms', verdana, arial;
    font-family: var(--mermaid-font-family); }

#mermaid-1581472930770 .grid path {
  stroke-width: 0; }


#mermaid-1581472930770 .today {
  fill: none;
  stroke: red;
  stroke-width: 2px; }



#mermaid-1581472930770 .task {
  stroke-width: 2; }

#mermaid-1581472930770 .taskText {
  text-anchor: middle;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581472930770 .taskText:not([font-size]) {
  font-size: 11px; }

#mermaid-1581472930770 .taskTextOutsideRight {
  fill: black;
  text-anchor: start;
  font-size: 11px;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581472930770 .taskTextOutsideLeft {
  fill: black;
  text-anchor: end;
  font-size: 11px; }


#mermaid-1581472930770 .task.clickable {
  cursor: pointer; }

#mermaid-1581472930770 .taskText.clickable {
  cursor: pointer;
  fill: #003163 !important;
  font-weight: bold; }

#mermaid-1581472930770 .taskTextOutsideLeft.clickable {
  cursor: pointer;
  fill: #003163 !important;
  font-weight: bold; }

#mermaid-1581472930770 .taskTextOutsideRight.clickable {
  cursor: pointer;
  fill: #003163 !important;
  font-weight: bold; }


#mermaid-1581472930770 .taskText0,
#mermaid-1581472930770 .taskText1,
#mermaid-1581472930770 .taskText2,
#mermaid-1581472930770 .taskText3 {
  fill: white; }

#mermaid-1581472930770 .task0,
#mermaid-1581472930770 .task1,
#mermaid-1581472930770 .task2,
#mermaid-1581472930770 .task3 {
  fill: #8a90dd;
  stroke: #534fbc; }

#mermaid-1581472930770 .taskTextOutside0,
#mermaid-1581472930770 .taskTextOutside2 {
  fill: black; }

#mermaid-1581472930770 .taskTextOutside1,
#mermaid-1581472930770 .taskTextOutside3 {
  fill: black; }


#mermaid-1581472930770 .active0,
#mermaid-1581472930770 .active1,
#mermaid-1581472930770 .active2,
#mermaid-1581472930770 .active3 {
  fill: #bfc7ff;
  stroke: #534fbc; }

#mermaid-1581472930770 .activeText0,
#mermaid-1581472930770 .activeText1,
#mermaid-1581472930770 .activeText2,
#mermaid-1581472930770 .activeText3 {
  fill: black !important; }


#mermaid-1581472930770 .done0,
#mermaid-1581472930770 .done1,
#mermaid-1581472930770 .done2,
#mermaid-1581472930770 .done3 {
  stroke: grey;
  fill: lightgrey;
  stroke-width: 2; }

#mermaid-1581472930770 .doneText0,
#mermaid-1581472930770 .doneText1,
#mermaid-1581472930770 .doneText2,
#mermaid-1581472930770 .doneText3 {
  fill: black !important; }


#mermaid-1581472930770 .crit0,
#mermaid-1581472930770 .crit1,
#mermaid-1581472930770 .crit2,
#mermaid-1581472930770 .crit3 {
  stroke: #ff8888;
  fill: red;
  stroke-width: 2; }

#mermaid-1581472930770 .activeCrit0,
#mermaid-1581472930770 .activeCrit1,
#mermaid-1581472930770 .activeCrit2,
#mermaid-1581472930770 .activeCrit3 {
  stroke: #ff8888;
  fill: #bfc7ff;
  stroke-width: 2; }

#mermaid-1581472930770 .doneCrit0,
#mermaid-1581472930770 .doneCrit1,
#mermaid-1581472930770 .doneCrit2,
#mermaid-1581472930770 .doneCrit3 {
  stroke: #ff8888;
  fill: lightgrey;
  stroke-width: 2;
  cursor: pointer;
  shape-rendering: crispEdges; }

#mermaid-1581472930770 .milestone {
  transform: rotate(45deg) scale(0.8, 0.8); }

#mermaid-1581472930770 .milestoneText {
  font-style: italic; }

#mermaid-1581472930770 .doneCritText0,
#mermaid-1581472930770 .doneCritText1,
#mermaid-1581472930770 .doneCritText2,
#mermaid-1581472930770 .doneCritText3 {
  fill: black !important; }

#mermaid-1581472930770 .activeCritText0,
#mermaid-1581472930770 .activeCritText1,
#mermaid-1581472930770 .activeCritText2,
#mermaid-1581472930770 .activeCritText3 {
  fill: black !important; }

#mermaid-1581472930770 .titleText {
  text-anchor: middle;
  font-size: 18px;
  fill: black;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581472930770 g.classGroup text {
  fill: #9370DB;
  stroke: none;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family);
  font-size: 10px; }
#mermaid-1581472930770   g.classGroup text .title {
    font-weight: bolder; }

#mermaid-1581472930770 g.clickable {
  cursor: pointer; }

#mermaid-1581472930770 g.classGroup rect {
  fill: #ECECFF;
  stroke: #9370DB; }

#mermaid-1581472930770 g.classGroup line {
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581472930770 .classLabel .box {
  stroke: none;
  stroke-width: 0;
  fill: #ECECFF;
  opacity: 0.5; }

#mermaid-1581472930770 .classLabel .label {
  fill: #9370DB;
  font-size: 10px; }

#mermaid-1581472930770 .relation {
  stroke: #9370DB;
  stroke-width: 1;
  fill: none; }

#mermaid-1581472930770 .dashed-line {
  stroke-dasharray: 3; }

#mermaid-1581472930770 #compositionStart {
  fill: #9370DB;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581472930770 #compositionEnd {
  fill: #9370DB;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581472930770 #aggregationStart {
  fill: #ECECFF;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581472930770 #aggregationEnd {
  fill: #ECECFF;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581472930770 #dependencyStart {
  fill: #9370DB;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581472930770 #dependencyEnd {
  fill: #9370DB;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581472930770 #extensionStart {
  fill: #9370DB;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581472930770 #extensionEnd {
  fill: #9370DB;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581472930770 .commit-id,
#mermaid-1581472930770 .commit-msg,
#mermaid-1581472930770 .branch-label {
  fill: lightgrey;
  color: lightgrey;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581472930770 .pieTitleText {
  text-anchor: middle;
  font-size: 25px;
  fill: black;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581472930770 .slice {
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581472930770 g.stateGroup text {
  fill: #9370DB;
  stroke: none;
  font-size: 10px;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581472930770 g.stateGroup text {
  fill: #9370DB;
  stroke: none;
  font-size: 10px; }

#mermaid-1581472930770 g.stateGroup .state-title {
  font-weight: bolder;
  fill: black; }

#mermaid-1581472930770 g.stateGroup rect {
  fill: #ECECFF;
  stroke: #9370DB; }

#mermaid-1581472930770 g.stateGroup line {
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581472930770 .transition {
  stroke: #9370DB;
  stroke-width: 1;
  fill: none; }

#mermaid-1581472930770 .stateGroup .composit {
  fill: white;
  border-bottom: 1px; }

#mermaid-1581472930770 .stateGroup .alt-composit {
  fill: #e0e0e0;
  border-bottom: 1px; }

#mermaid-1581472930770 .state-note {
  stroke: #aaaa33;
  fill: #fff5ad; }
#mermaid-1581472930770   .state-note text {
    fill: black;
    stroke: none;
    font-size: 10px; }

#mermaid-1581472930770 .stateLabel .box {
  stroke: none;
  stroke-width: 0;
  fill: #ECECFF;
  opacity: 0.5; }

#mermaid-1581472930770 .stateLabel text {
  fill: black;
  font-size: 10px;
  font-weight: bold;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

:root {
  --mermaid-font-family: '"trebuchet ms", verdana, arial';
  --mermaid-font-family: "Comic Sans MS", "Comic Sans", cursive; }

:root { --mermaid-font-family: "trebuchet ms", verdana, arial;}</style><style>#mermaid-1581472930770 {
    color: rgb(0, 0, 0);
    font: 16px "trebuchet ms", verdana, arial;
  }</style><g transform="translate(0, 0)"><g class="output"><g class="clusters"><g class="cluster" id="booking" transform="translate(244.5625,62.5)" style="opacity: 1;"><rect width="180.5625" height="109" x="-90.28125" y="-54.5"></rect><g class="label" transform="translate(0, -40.5)" id="mermaid-1581472930770Text"><g transform="translate(-27.75,-9.5)"><foreignObject width="55.5" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;">booking</div></foreignObject></g></g></g></g><g class="edgePaths"><g class="edgePath" style="opacity: 1;"><path class="path" d="M70.671875,62.5L112.4765625,62.5L154.28125,62.5L179.28125,62.5" marker-end="url(#arrowhead9993)" style="fill:none"></path><defs><marker id="arrowhead9993" viewBox="0 0 10 10" refX="9" refY="5" markerUnits="strokeWidth" markerWidth="8" markerHeight="6" orient="auto"><path d="M 0 0 L 10 5 L 0 10 z" class="arrowheadPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker></defs></g></g><g class="edgeLabels"><g class="edgeLabel" transform="translate(112.4765625,62.5)" style="opacity: 1;"><g transform="translate(-16.8046875,-9.5)" class="label"><foreignObject width="33.609375" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="edgeLabel">/pay</span></div></foreignObject></g></g></g><g class="nodes"><g class="node" id="C" transform="translate(244.5625,62.5)" style="opacity: 1;"><rect rx="5" ry="5" x="-65.28125" y="-19.5" width="130.5625" height="39" class="label-container"></rect><g class="label" transform="translate(0,0)"><g transform="translate(-55.28125,-9.5)"><foreignObject width="110.5625" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;">handlePayment</div></foreignObject></g></g></g><g class="node" style="opacity: 1;" id="A" transform="translate(39.3359375,62.5)"><rect rx="0" ry="0" x="-31.3359375" y="-19.5" width="62.671875" height="39" class="label-container"></rect><g class="label" transform="translate(0,0)"><g transform="translate(-21.3359375,-9.5)"><foreignObject width="42.671875" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;">Client</div></foreignObject></g></g></g></g></g></g></svg>