<svg id="mermaid-1581420435582" width="342.84375" xmlns="http://www.w3.org/2000/svg" height="125" viewBox="0 0 342.84375 125"><style>



#mermaid-1581420435582 .label {
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family);
  color: #333; }

#mermaid-1581420435582 .label text {
  fill: #333; }

#mermaid-1581420435582 .node rect,
#mermaid-1581420435582 .node circle,
#mermaid-1581420435582 .node ellipse,
#mermaid-1581420435582 .node polygon,
#mermaid-1581420435582 .node path {
  fill: #ECECFF;
  stroke: #9370DB;
  stroke-width: 1px; }

#mermaid-1581420435582 .node .label {
  text-align: center; }

#mermaid-1581420435582 .node.clickable {
  cursor: pointer; }

#mermaid-1581420435582 .arrowheadPath {
  fill: #333333; }

#mermaid-1581420435582 .edgePath .path {
  stroke: #333333;
  stroke-width: 1.5px; }

#mermaid-1581420435582 .edgeLabel {
  background-color: #e8e8e8;
  text-align: center; }

#mermaid-1581420435582 .cluster rect {
  fill: #ffffde;
  stroke: #aaaa33;
  stroke-width: 1px; }

#mermaid-1581420435582 .cluster text {
  fill: #333; }

#mermaid-1581420435582 div.mermaidTooltip {
  position: absolute;
  text-align: center;
  max-width: 200px;
  padding: 2px;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family);
  font-size: 12px;
  background: #ffffde;
  border: 1px solid #aaaa33;
  border-radius: 2px;
  pointer-events: none;
  z-index: 100; }

#mermaid-1581420435582 .actor {
  stroke: #CCCCFF;
  fill: #ECECFF; }

#mermaid-1581420435582 text.actor {
  fill: black;
  stroke: none; }

#mermaid-1581420435582 .actor-line {
  stroke: grey; }

#mermaid-1581420435582 .messageLine0 {
  stroke-width: 1.5;
  stroke-dasharray: '2 2';
  stroke: #333; }

#mermaid-1581420435582 .messageLine1 {
  stroke-width: 1.5;
  stroke-dasharray: '2 2';
  stroke: #333; }

#mermaid-1581420435582 #arrowhead {
  fill: #333; }

#mermaid-1581420435582 .sequenceNumber {
  fill: white; }

#mermaid-1581420435582 #sequencenumber {
  fill: #333; }

#mermaid-1581420435582 #crosshead path {
  fill: #333 !important;
  stroke: #333 !important; }

#mermaid-1581420435582 .messageText {
  fill: #333;
  stroke: none; }

#mermaid-1581420435582 .labelBox {
  stroke: #CCCCFF;
  fill: #ECECFF; }

#mermaid-1581420435582 .labelText {
  fill: black;
  stroke: none; }

#mermaid-1581420435582 .loopText {
  fill: black;
  stroke: none; }

#mermaid-1581420435582 .loopLine {
  stroke-width: 2;
  stroke-dasharray: '2 2';
  stroke: #CCCCFF; }

#mermaid-1581420435582 .note {
  stroke: #aaaa33;
  fill: #fff5ad; }

#mermaid-1581420435582 .noteText {
  fill: black;
  stroke: none;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family);
  font-size: 14px; }

#mermaid-1581420435582 .activation0 {
  fill: #f4f4f4;
  stroke: #666; }

#mermaid-1581420435582 .activation1 {
  fill: #f4f4f4;
  stroke: #666; }

#mermaid-1581420435582 .activation2 {
  fill: #f4f4f4;
  stroke: #666; }


#mermaid-1581420435582 .mermaid-main-font {
  font-family: "trebuchet ms", verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581420435582 .section {
  stroke: none;
  opacity: 0.2; }

#mermaid-1581420435582 .section0 {
  fill: rgba(102, 102, 255, 0.49); }

#mermaid-1581420435582 .section2 {
  fill: #fff400; }

#mermaid-1581420435582 .section1,
#mermaid-1581420435582 .section3 {
  fill: white;
  opacity: 0.2; }

#mermaid-1581420435582 .sectionTitle0 {
  fill: #333; }

#mermaid-1581420435582 .sectionTitle1 {
  fill: #333; }

#mermaid-1581420435582 .sectionTitle2 {
  fill: #333; }

#mermaid-1581420435582 .sectionTitle3 {
  fill: #333; }

#mermaid-1581420435582 .sectionTitle {
  text-anchor: start;
  font-size: 11px;
  text-height: 14px;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }


#mermaid-1581420435582 .grid .tick {
  stroke: lightgrey;
  opacity: 0.8;
  shape-rendering: crispEdges; }
#mermaid-1581420435582   .grid .tick text {
    font-family: 'trebuchet ms', verdana, arial;
    font-family: var(--mermaid-font-family); }

#mermaid-1581420435582 .grid path {
  stroke-width: 0; }


#mermaid-1581420435582 .today {
  fill: none;
  stroke: red;
  stroke-width: 2px; }



#mermaid-1581420435582 .task {
  stroke-width: 2; }

#mermaid-1581420435582 .taskText {
  text-anchor: middle;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581420435582 .taskText:not([font-size]) {
  font-size: 11px; }

#mermaid-1581420435582 .taskTextOutsideRight {
  fill: black;
  text-anchor: start;
  font-size: 11px;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581420435582 .taskTextOutsideLeft {
  fill: black;
  text-anchor: end;
  font-size: 11px; }


#mermaid-1581420435582 .task.clickable {
  cursor: pointer; }

#mermaid-1581420435582 .taskText.clickable {
  cursor: pointer;
  fill: #003163 !important;
  font-weight: bold; }

#mermaid-1581420435582 .taskTextOutsideLeft.clickable {
  cursor: pointer;
  fill: #003163 !important;
  font-weight: bold; }

#mermaid-1581420435582 .taskTextOutsideRight.clickable {
  cursor: pointer;
  fill: #003163 !important;
  font-weight: bold; }


#mermaid-1581420435582 .taskText0,
#mermaid-1581420435582 .taskText1,
#mermaid-1581420435582 .taskText2,
#mermaid-1581420435582 .taskText3 {
  fill: white; }

#mermaid-1581420435582 .task0,
#mermaid-1581420435582 .task1,
#mermaid-1581420435582 .task2,
#mermaid-1581420435582 .task3 {
  fill: #8a90dd;
  stroke: #534fbc; }

#mermaid-1581420435582 .taskTextOutside0,
#mermaid-1581420435582 .taskTextOutside2 {
  fill: black; }

#mermaid-1581420435582 .taskTextOutside1,
#mermaid-1581420435582 .taskTextOutside3 {
  fill: black; }


#mermaid-1581420435582 .active0,
#mermaid-1581420435582 .active1,
#mermaid-1581420435582 .active2,
#mermaid-1581420435582 .active3 {
  fill: #bfc7ff;
  stroke: #534fbc; }

#mermaid-1581420435582 .activeText0,
#mermaid-1581420435582 .activeText1,
#mermaid-1581420435582 .activeText2,
#mermaid-1581420435582 .activeText3 {
  fill: black !important; }


#mermaid-1581420435582 .done0,
#mermaid-1581420435582 .done1,
#mermaid-1581420435582 .done2,
#mermaid-1581420435582 .done3 {
  stroke: grey;
  fill: lightgrey;
  stroke-width: 2; }

#mermaid-1581420435582 .doneText0,
#mermaid-1581420435582 .doneText1,
#mermaid-1581420435582 .doneText2,
#mermaid-1581420435582 .doneText3 {
  fill: black !important; }


#mermaid-1581420435582 .crit0,
#mermaid-1581420435582 .crit1,
#mermaid-1581420435582 .crit2,
#mermaid-1581420435582 .crit3 {
  stroke: #ff8888;
  fill: red;
  stroke-width: 2; }

#mermaid-1581420435582 .activeCrit0,
#mermaid-1581420435582 .activeCrit1,
#mermaid-1581420435582 .activeCrit2,
#mermaid-1581420435582 .activeCrit3 {
  stroke: #ff8888;
  fill: #bfc7ff;
  stroke-width: 2; }

#mermaid-1581420435582 .doneCrit0,
#mermaid-1581420435582 .doneCrit1,
#mermaid-1581420435582 .doneCrit2,
#mermaid-1581420435582 .doneCrit3 {
  stroke: #ff8888;
  fill: lightgrey;
  stroke-width: 2;
  cursor: pointer;
  shape-rendering: crispEdges; }

#mermaid-1581420435582 .milestone {
  transform: rotate(45deg) scale(0.8, 0.8); }

#mermaid-1581420435582 .milestoneText {
  font-style: italic; }

#mermaid-1581420435582 .doneCritText0,
#mermaid-1581420435582 .doneCritText1,
#mermaid-1581420435582 .doneCritText2,
#mermaid-1581420435582 .doneCritText3 {
  fill: black !important; }

#mermaid-1581420435582 .activeCritText0,
#mermaid-1581420435582 .activeCritText1,
#mermaid-1581420435582 .activeCritText2,
#mermaid-1581420435582 .activeCritText3 {
  fill: black !important; }

#mermaid-1581420435582 .titleText {
  text-anchor: middle;
  font-size: 18px;
  fill: black;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581420435582 g.classGroup text {
  fill: #9370DB;
  stroke: none;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family);
  font-size: 10px; }
#mermaid-1581420435582   g.classGroup text .title {
    font-weight: bolder; }

#mermaid-1581420435582 g.clickable {
  cursor: pointer; }

#mermaid-1581420435582 g.classGroup rect {
  fill: #ECECFF;
  stroke: #9370DB; }

#mermaid-1581420435582 g.classGroup line {
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581420435582 .classLabel .box {
  stroke: none;
  stroke-width: 0;
  fill: #ECECFF;
  opacity: 0.5; }

#mermaid-1581420435582 .classLabel .label {
  fill: #9370DB;
  font-size: 10px; }

#mermaid-1581420435582 .relation {
  stroke: #9370DB;
  stroke-width: 1;
  fill: none; }

#mermaid-1581420435582 .dashed-line {
  stroke-dasharray: 3; }

#mermaid-1581420435582 #compositionStart {
  fill: #9370DB;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581420435582 #compositionEnd {
  fill: #9370DB;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581420435582 #aggregationStart {
  fill: #ECECFF;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581420435582 #aggregationEnd {
  fill: #ECECFF;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581420435582 #dependencyStart {
  fill: #9370DB;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581420435582 #dependencyEnd {
  fill: #9370DB;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581420435582 #extensionStart {
  fill: #9370DB;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581420435582 #extensionEnd {
  fill: #9370DB;
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581420435582 .commit-id,
#mermaid-1581420435582 .commit-msg,
#mermaid-1581420435582 .branch-label {
  fill: lightgrey;
  color: lightgrey;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581420435582 .pieTitleText {
  text-anchor: middle;
  font-size: 25px;
  fill: black;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581420435582 .slice {
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581420435582 g.stateGroup text {
  fill: #9370DB;
  stroke: none;
  font-size: 10px;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

#mermaid-1581420435582 g.stateGroup text {
  fill: #9370DB;
  stroke: none;
  font-size: 10px; }

#mermaid-1581420435582 g.stateGroup .state-title {
  font-weight: bolder;
  fill: black; }

#mermaid-1581420435582 g.stateGroup rect {
  fill: #ECECFF;
  stroke: #9370DB; }

#mermaid-1581420435582 g.stateGroup line {
  stroke: #9370DB;
  stroke-width: 1; }

#mermaid-1581420435582 .transition {
  stroke: #9370DB;
  stroke-width: 1;
  fill: none; }

#mermaid-1581420435582 .stateGroup .composit {
  fill: white;
  border-bottom: 1px; }

#mermaid-1581420435582 .stateGroup .alt-composit {
  fill: #e0e0e0;
  border-bottom: 1px; }

#mermaid-1581420435582 .state-note {
  stroke: #aaaa33;
  fill: #fff5ad; }
#mermaid-1581420435582   .state-note text {
    fill: black;
    stroke: none;
    font-size: 10px; }

#mermaid-1581420435582 .stateLabel .box {
  stroke: none;
  stroke-width: 0;
  fill: #ECECFF;
  opacity: 0.5; }

#mermaid-1581420435582 .stateLabel text {
  fill: black;
  font-size: 10px;
  font-weight: bold;
  font-family: 'trebuchet ms', verdana, arial;
  font-family: var(--mermaid-font-family); }

:root {
  --mermaid-font-family: '"trebuchet ms", verdana, arial';
  --mermaid-font-family: "Comic Sans MS", "Comic Sans", cursive; }

:root { --mermaid-font-family: "trebuchet ms", verdana, arial;}</style><style>#mermaid-1581420435582 {
    color: rgb(0, 0, 0);
    font: 16px "trebuchet ms", verdana, arial;
  }</style><g transform="translate(0, 0)"><g class="output"><g class="clusters"><g class="cluster" id="cart" transform="translate(244.5625,62.5)" style="opacity: 1;"><rect width="180.5625" height="109" x="-90.28125" y="-54.5"></rect><g class="label" transform="translate(0, -40.5)" id="mermaid-1581420435582Text"><g transform="translate(-14.4453125,-9.5)"><foreignObject width="28.890625" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;">cart</div></foreignObject></g></g></g></g><g class="edgePaths"><g class="edgePath" style="opacity: 1;"><path class="path" d="M70.671875,62.5L112.4765625,62.5L154.28125,62.5L179.28125,62.5" marker-end="url(#arrowhead952)" style="fill:none"></path><defs><marker id="arrowhead952" viewBox="0 0 10 10" refX="9" refY="5" markerUnits="strokeWidth" markerWidth="8" markerHeight="6" orient="auto"><path d="M 0 0 L 10 5 L 0 10 z" class="arrowheadPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker></defs></g></g><g class="edgeLabels"><g class="edgeLabel" transform="translate(112.4765625,62.5)" style="opacity: 1;"><g transform="translate(-16.8046875,-9.5)" class="label"><foreignObject width="33.609375" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;"><span class="edgeLabel">/pay</span></div></foreignObject></g></g></g><g class="nodes"><g class="node" id="B" transform="translate(244.5625,62.5)" style="opacity: 1;"><rect rx="5" ry="5" x="-65.28125" y="-19.5" width="130.5625" height="39" class="label-container"></rect><g class="label" transform="translate(0,0)"><g transform="translate(-55.28125,-9.5)"><foreignObject width="110.5625" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;">handlePayment</div></foreignObject></g></g></g><g class="node" style="opacity: 1;" id="A" transform="translate(39.3359375,62.5)"><rect rx="0" ry="0" x="-31.3359375" y="-19.5" width="62.671875" height="39" class="label-container"></rect><g class="label" transform="translate(0,0)"><g transform="translate(-21.3359375,-9.5)"><foreignObject width="42.671875" height="19"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; white-space: nowrap;">Client</div></foreignObject></g></g></g></g></g></g></svg>