---
title: Is it up to the newer developer to pay back others' technical debt?
date: 2020-02-03T03:23:12.338Z
description: >-
  And what you can do about it if you find yourself in such situation.
tags:
  - getting into a large codebase
---

You recently joined a new team. Maybe a new company.

As you get used to your new colleagues, you also discover the codebase they're working on. Very soon, you embark on features and bug fixes, as your team needs to deliver. So you dig into the existing code you don't know yet…

But after a few weeks digging into the Legacy Code to implement a feature, you might feel despair.

<p style="text-align: center">
 <img src="/assets/frustrated.gif" />
</p>

> "It's taken me a month so far and I'm still not done…"

> "The one who wrote the code I'm refactoring could get it all done in a day!"

I get it. Maybe the developers who wrote the code are still in the company. Maybe they're now working on new, _exciting_ projects!

And there you are, thinking that editing someone else's code doesn't feel as cool as writing something new. Wondering if it's the best use of your skills when more experienced developers could refactor the code in no time. 😩

Maybe you're a junior developer or an intern and you think it's natural to get the bitch work nobody else wants.

Or maybe you're an experienced developer who knows that it's part of the job, you don't always work on the interesting stuff.

## OK, stop a minute and think about it

First of all, that feeling of struggle is normal. Even after a few weeks.

It's [the nature of Legacy Code](/blog/what-is-legacy-code-is-it-code-without-tests).

> Legacy Code is the code you need to change and you struggle to understand.

That means it will get easier. As your experience with the codebase grows, you'll have more context and knowledge to refactor the code.

In fact, coding is not the problem. You recently joined a team. You need to get used to the system, tools, and code they have.

**After 1 year** you'll feel comfortable enough. Just be patient.

## How can you make the best use of your time?

There's actually something **you** can do better than anyone else.

Because you're in a very _unique_ position.

> Document It.

You're learning the system, tools, and code. You are giving it a fresh pair of eyes. You're bringing your point of view. You have the best point of view on what's clear and what's not!

**Regardless of your seniority**, you're the best person to point out the things that are not clear! And clarify them.

### Initiatives you can take already 🤠

- Some concepts you don't understand? Ask your colleagues and **write that knowledge somewhere**. Anything would do.
- **Build a glossary** of the jargon used within the team.
- A piece of code is hard to understand? **Add comments** as you discover what it's doing.
- You don't understand what this variable represents? **Rename it** when you figure it out!

Do that. Make the code a better place. A tiny bit at a time.

Little by little, file after file, you'll make the code better.

**Not good, but better.**

Progressively, you'll make the whole team uncover the unknowns, explicit the implicit. You'll gain trust as your expertise grows.

Others will really appreciate you for it.

And you'll finally enjoy working on others' technical debt!
