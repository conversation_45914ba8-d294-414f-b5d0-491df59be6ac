---
title: "Design Patterns, Empathy, and Reading Complex Code"
date: 2021-08-24T20:15:00.000Z
image: /assets/legacy-of-socrates-6th-edition.png
description: >-
  4 great talks on Legacy Code that I had the pleasure to host. A focus on empathy and getting into complex codebases.
tags:
  - conference
  - getting into a large codebase
  - not sure where to start refactoring
  - making others care about it
---

On June 17, 2021, I co-organized the 6th edition of The Legacy of SoCraTes with my partner in crime [<PERSON>](https://twitter.com/adibolb).

It's a half-a-day remote conference where 4 speakers share their tips to deal with Legacy Code.

![The Legacy of SoCraTes](/assets/legacy-of-socrates-6th-edition.png)
![by <PERSON> and <PERSON>](/assets/legacy-of-socrates-organizers.png)

This edition had a focus on empathy. Working with a legacy system can be demanding. One thing you can control is your approach to such a challenge. <PERSON> and <PERSON><PERSON><PERSON> shared insightful stories to help you there.

I was also really glad to have <PERSON> presenting concrete Design Patterns you can use to refactor any legacy codebase.

Finally, Dr. <PERSON><PERSON><PERSON> gave an eye-opening talk on how to read complex code. Get ready to dive into how your brain works! 🧠

As usual, we recorded all the talks so you can watch them now and share them with your friends and colleagues!

## Design patterns for modernizing legacy codebases

[<PERSON> Noback](https://twitter.com/matthiasnoback) is one of my favorite sources of knowledge. His book [Advanced Web Application Architecture](https://matthiasnoback.nl/book/advanced-web-application-architecture/) is tremendously useful when working on the web.

I really like how Matthias approaches architecture from _existing code_. More often than not, I have to work with an imperfect, existing codebase. Having tools and techniques to refactor such a system is gold!

This talk is a sneak peek of the book. Discover a few design patterns that can really help you refactor existing code:

<iframe width="560" height="315" src="https://www.youtube-nocookie.com/embed/WI1QY6OMglE" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

## Empathy is a Technical Skill

If you don't know [Andrea Goulet](https://twitter.com/andreagoulet) already, you're missing out! 😜

No, but seriously. Andrea is the co-founder of [Corgibytes](https://corgibytes.com/) which is specialized in software remodeling. She also created the [Legacy Code Rocks](https://www.legacycode.rocks/) podcast and community.

In her talk, she explains how empathy is much more than a "soft skill".

She gives practical and actionable "next actions" you can start doing to improve the way you're building software.

If you’ve ever thought that you’re “good with machines” but have struggled to understand people, this is a talk you won’t want to miss.

<iframe width="560" height="315" src="https://www.youtube-nocookie.com/embed/8COwvND6wTI" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

Or, as Donald Knuth nicely said:

> Programming is the art of telling another human being what one wants the computer to do.

## Inside-Out TDDDD

Yes, that's 4 Ds.

This stands for "Inside-Out TDD-Driven Development". Behind this intriguing title, [Amitai Schleier](https://twitter.com/schmonz) tells personal stories about the impact of software craft on emotional states and working relationships.

And we're still very much in the #empathy here!

Amitai genuinely shares his path towards greater joy and humanity in software development. I think it matters. ❤️

<iframe width="560" height="315" src="https://www.youtube-nocookie.com/embed/EZUf6hDipQk" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

## How to Read Complex Code

[Dr. Felienne Hermans](https://twitter.com/felienne) is the author of [The Programmer's Brain](https://www.felienne.com/book). She focuses on how your brain works, especially when working with unfamiliar code.

How to read code more efficiently? How to understand complex code? How to be more effective and empathic?

Felienne gives some insights in her talk. I really like how you can experiment it yourself with her little experiment.

Let's dive into Long-Term Memory, Short-Term Memory, and other brain concepts:

<iframe width="560" height="315" src="https://www.youtube-nocookie.com/embed/jjMlguOrWHc" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

## "These talks are really great! Can I have more?"

Sure thing!

There's no fixed schedule, but I like to organize this short conference every few months. I reach out to a few speakers to build an interesting agenda, then we set up the event.

Everything is FREE. Attending is the opportunity to directly ask questions to these speakers, from the comfort of your home.

If that's something you want to hear about, **subscribe to my newsletter** below 👇

I publish my monthly tips on Legacy Code here. I also tell my subscribers in advance about the conference and the agenda, so they don't miss out. Join them!
