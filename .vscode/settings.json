{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "always"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.rulers": [80], "eslint.probe": ["javascript", "javascriptreact", "json", "jsonc", "markdown", "typescript", "typescriptreact", "yaml"], "eslint.rules.customizations": [{"rule": "*", "severity": "warn"}], "typescript.tsdk": "node_modules/typescript/lib", "typescript.preferences.organizeImports": {"caseFirst": "lower"}, "[cpp]": {"editor.defaultFormatter": "ms-vscode.cpptools"}, "[astro]": {"editor.defaultFormatter": "astro-build.astro-vscode"}}