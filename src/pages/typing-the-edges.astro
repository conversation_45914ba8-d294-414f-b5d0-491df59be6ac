---
import Title from "@components/Title.astro"
import GuideLayout from "@layouts/GuideLayout.astro"
---

<GuideLayout
  title="Types at the edges of your system"
  description="Your TypeScript codebase has blind spots. Here is how to find and fix them!"
>
  <Title>Types at the edges of your system</Title>

  <blockquote>
    <p>Find and fix your TypeScript blind spots</p>
  </blockquote>

  <p>
    <span role="img" aria-label=""> 😃 </span>
     Coding with TypeScript feels safe, the compiler has your back.
  </p>
  <p>
    <span role="img" aria-label=""> 😨 </span>
     But today, you got a critical issue in prod: payments are failing! A quick check of the logs left you puzzled:{" "}
    <code>TypeError: discount.toFixed is not a function</code>.
  </p>
  <p>
    <span role="img" aria-label=""> 😕 </span>
     Wait… Isn't <code>discount</code> supposed to be a number here? How did that happen? Why didn't TS catch it before?!
    Worry no more.
  </p>
  <p>
    <span role="img" aria-label=""> 🤠 </span>
     In this talk, I will show you where your blind spots are, and a simple way for you to fix them.
  </p>

  <p class="text-center">
    <img
      src="/assets/typing-the-edges.png"
      alt="Mazda head units got bricked by a local NPR station in Seattle in February 2022. Better validation at the edges would have save them a lot of trouble (and money)."
    />
  </p>

  <h2>
    <span role="img" aria-label="toolbox"> 🧰 </span>
    &nbsp;Resources
  </h2>
  <ul>
    <li>
      <a
        href="https://understandlegacycode.com/assets/talks/types-at-the-edges-of-your-system.pdf"
        target="_blank"
        rel="noopener noreferrer"
      >
        Slides of my talk
      </a>
    </li>
    <li>
      <a href="https://stackoverflow.com/a/50647536" target="_blank" rel="noopener noreferrer">
        Why <code>array[0]</code> doesn't return <code>T | undefined</code>{" "}
        by default?
      </a>
    </li>
    <li>
      <a
        href="https://lexi-lambda.github.io/blog/2019/11/05/parse-don-t-validate/"
        target="_blank"
        rel="noopener noreferrer"
      >
        Parse, Don't Validate
      </a>
    </li>
    <li>
      <a href="https://www.jonmellman.com/posts/typescript-for-api-contracts" target="_blank" rel="noopener noreferrer">
        Advanced TypeScript Patterns: API Contracts
      </a>
    </li>
    <li>
      <a href="https://www.youtube.com/watch?v=rY_XqfSHock" target="_blank" rel="noopener noreferrer">
        Fixing TypeScript's Blindspots: Runtime Typechecking
      </a>
    </li>
    <li>
      <a href="https://github.com/colinhacks/zod" target="_blank" rel="noopener noreferrer"> colinhacks/zod </a>{" "}
      the TypeScript-first schema validation with static type inference
    </li>
  </ul>
</GuideLayout>
