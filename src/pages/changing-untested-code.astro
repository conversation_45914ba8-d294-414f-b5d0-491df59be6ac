---
import Title from "@components/Title.astro"
import GuideLayout from "@layouts/GuideLayout.astro"
---

<GuideLayout
  title="Changing untested code without breaking it"
  description="Without tests, every code change is risky. But how to put tests on a code that wasn't design for it?"
  tag="changing untested code"
>
  <Title>Changing untested code without breaking it</Title>

  <p>
    In general, Legacy Code was not designed to be testable. Putting the existing codebase into a test suite is a very
    challenging step.
  </p>

  <p>Problem is: without tests, every code change is risky. How do you know if you broke something?</p>

  <p>
    Let's see what can get in the way and how you can solve every obstacle. Let's put some automated tests on your
    codebase!
  </p>
</GuideLayout>
