---
import Title from "@components/Title.astro"
import GuideLayout from "@layouts/GuideLayout.astro"
---

<GuideLayout
  title="I don't want to make things worse!"
  description="Not sure if a pattern will make the code more maintainable? Here are a few resources that will help."
  tag="best practice or code smell?"
>
  <Title>I don't want to make things worse!</Title>

  <p>There are plenty of patterns you can apply to code to make it more maintainable.</p>

  <p>
    But sometimes, it's hard to see what are the actual benefits of these patterns. Will they actually make the code
    easier to change? Aren't you just trading a complexity for another?
  </p>

  <p>
    If you feel confused about what direction to take to address technical debt, here are a few resources that will
    help.
  </p>
</GuideLayout>
