---
import Title from "@components/Title.astro"
import GuideLayout from "@layouts/GuideLayout.astro"
---

<GuideLayout
  title="AI Support"
  description="How Artificial Intelligence can help you analyze a large codebase"
  tag="ai"
>
  <Title>AI Support</Title>

  <blockquote>
    <p>Can AI help me tame legacy codebases?</p>
  </blockquote>

  <p>
    With the development of AI and the rise of ChatGPT, this is a relevant question to ask. Sure, it can generate new
    code you have to maintain. But can it help you maintain existing code?
  </p>

  <h2>
    <span role="img" aria-label="camera">🎥</span>
    &nbsp;Talks
  </h2>

  <ul>
    <li>
      <a href="https://www.youtube.com/watch?v=iXGBIX8gudE" target="_blank" rel="noopener noreferrer">
        ChatGPT & Copilot are NOT Refactoring Tools
      </a>{" "}
      is a short explanation from <PERSON> on why AI is technically not a refactoring tool.
    </li>
  </ul>

  <h2>
    <span role="img" aria-label="toolbox">🧰</span>
    &nbsp;Tools
  </h2>

  <p>There are many tools popping up around, here are some I want to highlight:</p>

  <ul>
    <li>
      <a href="https://www.codium.ai/" target="_blank" rel="noopener noreferrer">CodiumAI</a> can help you generate tests
      on existing code. I gave them a hand on the VS Code extension in early 2023.
    </li>
    <li>
      <a
        href="https://marketplace.visualstudio.com/items?itemName=Rubberduck.rubberduck-vscode"
        target="_blank"
        rel="noopener noreferrer"
      >
        Rubberduck for VS Code
      </a>
      . I also contributed to this one. It's an open-source extension that can be an helpful assistant.
    </li>
    <li>
      <a href="https://denigma.app/" target="_blank" rel="noopener noreferrer">Denigma</a> which seems to be really good
      at explaining what spaghetti code is doing.
    </li>
  </ul>

  <h2>
    <span role="img" aria-label="microscope">🔬</span>
    &nbsp;Studies
  </h2>

  <p>Here are interesting studies related to the usage of AI to deal with existing codebases:</p>

  <ul>
    <li>
      <a
        href="https://codescene.com/hubfs/whitepapers/Refactoring-vs-Refuctoring-Advancing-the-state-of-AI-automated-code-improvements.pdf"
        target="_blank"
        rel="noopener noreferrer"
      >
        Refactoring vs. Refuctoring
      </a>{" "}
      by CodeScene. They figured that LLMs will generally introduce a regression when attempting to refactor complex code.
      But coupled to a fact-checking validation step, tooling could leverage these and only suggest the ones that actually
      work. This is promising because it would unlock more powerful automated refactorings.
    </li>
    <li>
      <a href="https://arxiv.org/pdf/2312.12450.pdf" target="_blank" rel="noopener noreferrer">
        Can It Edit? Evaluating the Ability of Large Language Models to Follow Code Editing Instructions.
      </a>
      This one concludes that it's a good idea to train models on the codebase commits. Also, closed and distilled models
      seem to be doing a better job than open ones. Finally, the more specific the instructions, the better.
    </li>
  </ul>
</GuideLayout>
