---
import { Icon } from "astro-icon/components"

interface Props {
  /**
   * The quote content that will be displayed
   */
  children: any
  /**
   * The name of the person who said the quote
   */
  author?: string
}

const { author } = Astro.props
---

<blockquote class="border-primary-500 border-l-4 pl-4">
  <div>
    <Icon name="lucide:quote" size="2rem" />
    <slot />
    <Icon name="lucide:quote" size="2rem" />
  </div>
  {author && <cite class="block text-sm font-medium">{author}</cite>}
</blockquote>

<style lang="scss" is:global>
  blockquote {
    font-size: var(--font-size-2);

    p {
      display: inline;
    }

    svg {
      display: inline;
      margin-block-start: calc(var(--space-s) * -1);

      &:first-child {
        scale: -1 1;
      }
    }

    cite {
      margin-block-start: var(--space-s);
      font-size: var(--font-size-1);
    }
  }
</style>
