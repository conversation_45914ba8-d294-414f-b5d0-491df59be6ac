---
import { DarkM<PERSON>, SkipLink } from "accessible-astro-components"
import { Icon } from "astro-icon/components"
import Navigation from "../components/Navigation.astro"
---

<header>
  <SkipLink />
  <Navigation>
    <li class="menu-item">
      <a href="/">Home</a>
    </li>
    <li class="menu-item">
      <a href="/all-articles">Articles</a>
    </li>
    <li class="menu-item">
      <a href="/bookshelf">Books</a>
    </li>
    <li class="menu-item first-aid">
      <a href="/first-aid-kit" target="_blank" rel="noreferrer noopener"> ⛑️ First Aid Kit </a>
    </li>
    <li class="menu-item type-icon animate-rotate">
      <DarkMode>
        <Icon name="lucide:moon" slot="light" />
        <Icon name="lucide:sun" slot="dark" />
      </DarkMode>
    </li>
  </Navigation>
</header>

<style lang="scss" is:global>
  header {
    .type-icon {
      display: block;
      margin-inline: -7px;

      svg {
        inline-size: 30px;
        block-size: 30px;
      }
    }
  }
</style>
