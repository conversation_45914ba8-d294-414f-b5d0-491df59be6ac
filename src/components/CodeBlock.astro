<!--
  This component enhances existing code blocks generated by Astro's <PERSON><PERSON> integration
  It adds copy buttons and language labels to all <pre> elements in the blog content
-->
<script>
  function getColorFromString(str: string): string {
    if (!str) return "#6b7280"

    let hash = 0
    for (let i = 0; i < str.length; i++) {
      hash = str.charCodeAt(i) + ((hash << 5) - hash)
    }

    const hue = Math.abs(hash) % 360
    return `hsl(${hue}, 60%, 45%)`
  }

  function enhanceCodeBlocks() {
    // Find all pre elements that contain code (generated by <PERSON><PERSON>)
    const preElements = document.querySelectorAll(".blog-content pre")

    preElements.forEach((pre) => {
      // Skip if already enhanced
      if (pre.classList.contains("enhanced")) return

      // Wrap code block
      pre.classList.add("enhanced")
      const wrapper = document.createElement("div")
      wrapper.className = "code-block-enhanced relative my-6"
      pre.parentNode?.insertBefore(wrapper, pre)
      wrapper.appendChild(pre)

      // Get language from <PERSON><PERSON>'s data attribute or class
      let language = "plaintext"

      // Try to get language from data-language attribute
      if (pre.hasAttribute("data-language")) {
        language = pre.getAttribute("data-language") || "plaintext"
      } else {
        // Fallback: try to get from class attribute
        const codeElement = pre.querySelector("code")
        if (codeElement) {
          const classList = Array.from(codeElement.classList)
          const langClass = classList.find((cls) => cls.startsWith("language-"))
          if (langClass) {
            language = langClass.replace("language-", "")
          }
        }
      }

      // Add language label
      if (language !== "plaintext" && language !== "") {
        const languageLabel = document.createElement("div")
        languageLabel.className =
          "language-label absolute top-0 left-4 z-10 rounded-b p-2 text-xs font-mono font-bold text-white uppercase tracking-wide"
        languageLabel.style.backgroundColor = getColorFromString(language)
        languageLabel.textContent = language
        wrapper.prepend(languageLabel)
      }

      // Add copy button
      const copyButton = document.createElement("button")
      copyButton.className =
        "copy-button absolute right-4 top-4 z-10 rounded bg-gray-700 px-3 py-1 text-xs text-white opacity-75 transition-opacity hover:opacity-100 focus:opacity-100"
      copyButton.setAttribute("aria-label", "Copy code to clipboard")

      const copyText = document.createElement("span")
      copyText.className = "copy-text"
      copyText.textContent = "Copy"

      const copiedText = document.createElement("span")
      copiedText.className = "copied-text hidden"
      copiedText.textContent = "Copied!"

      copyButton.appendChild(copyText)
      copyButton.appendChild(copiedText)
      wrapper.appendChild(copyButton)

      copyButton.addEventListener("click", async () => {
        try {
          const codeText = pre.textContent || ""
          await navigator.clipboard.writeText(codeText)

          // Show "Copied!" feedback
          copyText.classList.add("hidden")
          copiedText.classList.remove("hidden")

          // Reset after 2 seconds
          setTimeout(() => {
            copyText.classList.remove("hidden")
            copiedText.classList.add("hidden")
          }, 2000)
        } catch (err) {
          console.error("Failed to copy code:", err)
        }
      })

      // Add some styling to the pre element
      const preElement = pre as HTMLPreElement
      preElement.style.position = "relative"
      preElement.style.paddingTop = "3rem" // Make room for the copy button
    })
  }

  // Initialize on page load
  document.addEventListener("DOMContentLoaded", enhanceCodeBlocks)

  // Re-initialize on Astro page transitions
  document.addEventListener("astro:page-load", enhanceCodeBlocks)
</script>

<style lang="scss" is:global>
  .code-block-enhanced {
    position: relative;

    pre[data-language]:not([data-language="plaintext"]) .line {
      // If there's a language set, add line count in the gutter
      &::before {
        display: inline-block;
        counter-increment: line;
        margin-right: 20px;
        margin-left: -10px;
        width: 20px;
        content: counter(line);
        -webkit-user-select: none;
        user-select: none;
        text-align: right;
      }

      // Except if there's only one line
      &:first-of-type:last-of-type::before {
        content: "";
      }
    }

    pre {
      border-radius: 0.5rem;
      line-height: 1.5;
      font-family: "Fira Code", "Monaco", "Cascadia Code", "Roboto Mono", monospace;
    }

    code {
      counter-reset: line;
      background-color: transparent;
    }

    .line.highlighted {
      display: inline-block;
      margin: 0 -1rem;
      box-shadow: inset 4px 0 0 0 var(--brand-primary);
      background-color: var(--brand-background);
      padding: 1px 1rem;
      width: 100%;
    }
  }

  .language-label {
    border-radius: 0 0 4px 4px;
    font-size: 0.75rem;
    line-height: 1;
    user-select: none;
  }

  .copy-button:hover {
    opacity: 1;
  }

  .copy-button:focus {
    opacity: 1;
    outline: 2px solid var(--brand-primary);
    outline-offset: 2px;
  }
</style>
