<div class="callout">
  <em id="callout-title">Struggling with Legacy Code and not enough time to clean it up?</em>
  <br />
  <span role="img" aria-label="Rescue Worker Helmet"> ⛑️️ </span>{" "}
  <strong>
    <a id="callout-link" href="/first-aid-kit">My First Aid Kit</a>
  </strong>{" "}
  <span id="callout-text">
    can help you rescue any codebase <strong>quickly</strong> and <strong>safely</strong>!
  </span>
</div>

<script>
  const isFrench = window.navigator.language.startsWith("fr")
  if (isFrench) {
    const calloutTitle = document.getElementById("callout-title")
    if (calloutTitle) {
      calloutTitle.textContent = "Trop de code Legacy à maintenir et pas assez de temps pour le nettoyer ?"
    }

    const calloutLink = document.getElementById("callout-link")
    if (calloutLink && "href" in calloutLink) {
      calloutLink.href = "/premiers-soins"
      calloutLink.textContent = "Mon kit Premiers Soins"
    }

    const calloutText = document.getElementById("callout-text")
    if (calloutText) {
      calloutText.innerHTML = "peut secourir votre code <strong>rapidement</strong> et <strong>sereinement</strong> !"
    }
  }
</script>

<style lang="scss" is:global>
  .callout {
    margin: 3rem 0;
    border-left: 5px var(--first-aid-primary) solid;
    border-radius: 0 0.5rem 0.5rem 0;
    background: var(--first-aid-background);
    padding: 0.75rem 0 0.75rem 1rem;

    a {
      box-shadow: inset 0 -2px 0 var(--first-aid-primary);
    }
    a:hover,
    a:focus,
    a:active {
      box-shadow: inset 0 -1.3em 0 var(--first-aid-background);
    }
  }
</style>
