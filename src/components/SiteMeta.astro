---
/**
 * SiteMeta Component
 *
 * @description Component to handle site metadata and essential head elements
 */
interface Props {
  /**
   * The title of the page
   */
  title: string
  /**
   * The description of the page
   */
  description: string
  /**
   * The URL of the page
   */
  url: string
  /**
   * The image path for social sharing
   */
  image: string
  /**
   * The author of the page
   */
  author?: string
}

const { title, description, url, image, author = "Mark Teekman" } = Astro.props

let subtitle = "Change Messy Software Without Breaking It"
---

<!-- general meta -->
<meta name="title" content={`${title} - ${subtitle}`} />
<meta name="description" content={description} />
<meta name="author" content={author} />

<!-- open graph -->
<meta property="og:title" content={`${title} - ${subtitle}`} />
<meta property="og:description" content={description} />
<meta property="og:type" content="website" />
<meta property="og:url" content={url} />
<meta property="og:image" content={`${Astro.site}${image}`} />

<!-- twitter card -->

<!-- page title -->
<title>{title}</title>
