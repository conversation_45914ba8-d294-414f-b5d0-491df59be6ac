---
import ResponsiveToggle from "./ResponsiveToggle.astro"
---

<div id="main-navigation" class="pt-6">
  <div class="wrapper w-full">
    <nav class="desktop-menu w-full" aria-label="Main navigation desktop">
      <ul class="menu">
        <slot />
      </ul>
    </nav>
    <ResponsiveToggle />
  </div>
  <nav class="mobile-menu" aria-label="Main navigation mobile">
    <ul class="menu align-start flex flex-col justify-between space-y-2 pt-5">
      <slot />
    </ul>
  </nav>
</div>

<script>
  document.addEventListener("astro:page-load", () => {
    // variables
    const mainNav = document.querySelector("#main-navigation") as HTMLElement | null
    if (!mainNav) return

    const mainMenu = mainNav.querySelector("ul") as HTMLUListElement | null
    const dropdownMenus = [...document.querySelectorAll(".has-dropdown button")] as HTMLButtonElement[]

    // functions
    const setActiveMenuItem = (): void => {
      const mobileDesktopMenus = mainNav.querySelectorAll("nav > ul")
      const currentPathname = window.location.pathname

      mobileDesktopMenus.forEach((menu) => {
        const menuItems = [...menu.querySelectorAll('a:not([rel*="external"])')] as HTMLAnchorElement[]

        menuItems.forEach((menuItem) => {
          if (currentPathname.includes(menuItem.pathname.replaceAll("/", "")) && menuItem.textContent !== "Home") {
            menuItem.classList.add("is-active")
            menuItem.setAttribute("aria-current", "page")
          } else if (menuItem.textContent === "Home" && currentPathname === "/") {
            menuItem.classList.add("is-active")
            menuItem.setAttribute("aria-current", "page")
          }
        })
      })
    }

    const isOutOfViewport = (element: Element): boolean => {
      const elementBounds = element.getBoundingClientRect()
      return elementBounds.right > (window.innerWidth || document.documentElement.clientWidth)
    }

    const openDropdownMenu = (dropdownMenu: HTMLButtonElement): void => {
      const dropdownList = dropdownMenu.parentNode?.querySelector("ul") as HTMLUListElement | null
      if (!dropdownList) return

      dropdownMenu.classList.add("show")
      dropdownMenu.setAttribute("aria-expanded", "true")

      if (isOutOfViewport(dropdownList)) {
        dropdownList.style.left = "auto"
      }
    }

    const closeDropdownMenu = (dropdownMenu: HTMLButtonElement): void => {
      dropdownMenu.classList.remove("show")
      dropdownMenu.setAttribute("aria-expanded", "false")
    }

    const closeAllDropdownMenus = (): void => {
      for (let i = 0; i < dropdownMenus.length; i++) {
        closeDropdownMenu(dropdownMenus[i])
      }
    }

    const toggleDropdownMenu = (event: MouseEvent): void => {
      const target = event.target as HTMLButtonElement
      if (target.getAttribute("aria-expanded") === "false") {
        closeAllDropdownMenus()
        openDropdownMenu(target)
      } else {
        closeDropdownMenu(target)
      }
    }

    // execution
    mainMenu &&
      mainMenu.addEventListener("keydown", (event: KeyboardEvent) => {
        const element = event.target as Element
        const currentMenuItem = element.closest("li")
        const menuItems = [...mainMenu.querySelectorAll(".menu-item")] as HTMLLIElement[]
        const currentDropdownMenu = element.closest(".has-dropdown button") as HTMLButtonElement | null
        const currentDropdownMenuItem = element.closest(".has-dropdown li") as HTMLLIElement | null
        const currentIndex = currentMenuItem ? menuItems.findIndex((item) => item === currentMenuItem) : -1

        const key = event.key
        let targetItem: Element | null = null

        if (key === "ArrowRight") {
          if (currentMenuItem && menuItems.indexOf(currentMenuItem as HTMLLIElement) === menuItems.length - 1) {
            targetItem = menuItems[0]
          } else if (currentMenuItem) {
            targetItem = menuItems[currentIndex + 1]
          }
        }

        if (key === "ArrowLeft") {
          if (currentMenuItem && menuItems.indexOf(currentMenuItem as HTMLLIElement) === 0) {
            targetItem = menuItems[menuItems.length - 1]
          } else if (currentMenuItem) {
            targetItem = menuItems[currentIndex - 1]
          }
        }

        if (key === "Escape") {
          targetItem = menuItems[0]
        }

        if (currentDropdownMenu) {
          const nextElement = currentDropdownMenu.nextElementSibling as Element | null
          if (nextElement) {
            const firstDropdownItem = nextElement.querySelector("li")

            if (key === "ArrowDown") {
              event.preventDefault()
              openDropdownMenu(currentDropdownMenu)
              targetItem = firstDropdownItem
            }
          }

          if (key === "Escape") {
            closeDropdownMenu(currentDropdownMenu)
          }
        }

        if (currentDropdownMenuItem) {
          const currentDropdownList = currentDropdownMenuItem.parentNode as Element | null
          if (currentDropdownList) {
            const dropdownMenuItems = [...currentDropdownList.querySelectorAll("li")] as HTMLLIElement[]
            const currentIndex = dropdownMenuItems.findIndex((item) => item === currentDropdownMenuItem)

            if (key === "ArrowDown") {
              event.preventDefault()

              if (dropdownMenuItems.indexOf(currentDropdownMenuItem) === dropdownMenuItems.length - 1) {
                targetItem = dropdownMenuItems[0]
              } else {
                targetItem = dropdownMenuItems[currentIndex + 1]
              }
            }

            if (key === "ArrowUp") {
              event.preventDefault()

              if (dropdownMenuItems.indexOf(currentDropdownMenuItem) === 0) {
                targetItem = dropdownMenuItems[dropdownMenuItems.length - 1]
              } else {
                targetItem = dropdownMenuItems[currentIndex - 1]
              }
            }

            if (key === "Escape") {
              const currentDropdownMenu = currentDropdownList.previousElementSibling as HTMLButtonElement | null
              if (currentDropdownMenu) {
                targetItem = currentDropdownMenu.parentElement
                closeAllDropdownMenus()
              }
            }

            if (key === "Tab") {
              const currentDropdownMenu = currentDropdownList.previousElementSibling as HTMLButtonElement | null
              if (currentDropdownMenu) {
                if (dropdownMenuItems.indexOf(currentDropdownMenuItem) === dropdownMenuItems.length - 1) {
                  closeDropdownMenu(currentDropdownMenu)
                }
              }
            }
          }
        }

        if (targetItem) {
          const focusableElement = targetItem.querySelector("a, button, input") as HTMLElement | null
          if (focusableElement) {
            focusableElement.focus()
          }
        }
      })

    dropdownMenus &&
      dropdownMenus.forEach((dropdownMenu) => {
        dropdownMenu.addEventListener("click", toggleDropdownMenu as EventListener)
      })

    setActiveMenuItem()
    window.addEventListener("click", (event: MouseEvent) => {
      const element = event.target as Element
      if (!element.hasAttribute("aria-haspopup") && !element.classList.contains("submenu-item")) {
        closeAllDropdownMenus()
      }
    })
  })
</script>

<style lang="scss" is:global>
  .mobile-menu {
    ul,
    li,
    ol {
      list-style: none;
    }

    ul > li,
    ul > li > button {
      margin-left: 0 !important;
    }

    ul > li::before {
      display: none !important;
    }

    a {
      box-shadow: none;
    }
  }

  .desktop-menu {
    ul,
    li,
    ol {
      display: flex;
      gap: 1rem;
      margin: 0;
      padding: 0;
      list-style: none;
    }

    ul,
    ul > li {
      margin-left: 0 !important;
    }

    ul > li::before {
      display: none !important;
    }

    ul > li::after {
      position: relative;
      content: "~";
      color: var(--brand-primary);
      font-weight: 600;
    }
    ul > li:last-of-type::after {
      content: "";
    }

    ul > li > a {
      position: relative;
      -webkit-box-decoration-break: clone;
      box-decoration-break: clone;
      box-shadow: none !important;
      height: fit-content;
      text-decoration: none;

      &:after {
        position: absolute;
        right: 0;
        bottom: -2px;
        left: 0;
        transform: translateZ(0) scaleX(0);
        transform-origin: left center;
        transition: all 0.15s ease-in-out;
        background-image: linear-gradient(to left, var(--brand-primary), var(--brand-primary-light));
        height: 3px;
        content: "";
      }

      &:hover,
      &:focus,
      &:active {
        &::after {
          transform: translateZ(0) scale(1);
        }
      }
    }

    ul > li.first-aid > a {
      color: var(--first-aid-primary);
      font-weight: 500;

      &:after {
        background-image: linear-gradient(to left, var(--first-aid-primary), var(--first-aid-primary-light));
      }
    }
  }

  #main-navigation {
    margin-bottom: 1.75rem;
    letter-spacing: -0.015em;
    text-transform: uppercase;

    .wrapper {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    // Mobile-first: show mobile menu by default
    .mobile-menu {
      display: none;

      &.show {
        display: block;
      }

      ul {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
      }
    }

    .responsive-toggle {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .desktop-menu {
      display: none;
    }

    // Desktop: switch to desktop menu at larger screens
    @media (min-width: 768px) {
      .mobile-menu {
        display: none;
      }

      .responsive-toggle {
        display: none;
      }

      .desktop-menu {
        display: block;
      }
    }

    .darkmode-toggle {
      margin-left: 1rem;
      border: none;
      padding: 0;

      .icon {
        width: 30px;
        height: 30px;
      }

      &:where(:hover, :focus-visible) {
        box-shadow: none;
      }
    }
  }
</style>
