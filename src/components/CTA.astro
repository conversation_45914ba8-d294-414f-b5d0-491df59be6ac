---
interface Props {
  variant?: "regular" | "tilted" | "coming-soon" | "first-aid-kit" | "premiers-soins"
}

const { variant = "regular" } = Astro.props

// Set the appropriate UID based on variant
const formUid =
  variant === "coming-soon"
    ? "ee438164c6"
    : variant === "first-aid-kit"
      ? "084305275e"
      : variant === "premiers-soins"
        ? "45bef52aca"
        : "1c42a88117"

const isTilted = variant === "tilted"
---

<div class={`cta-form ${isTilted ? "tilted-form" : "regular-form"}`} id={`cta-${formUid}`}>
  <!-- The ConvertKit script will be loaded and form will be injected here -->
</div>

<script define:vars={{ formUid, isTilted }}>
  // Load ConvertKit form script
  function loadCTAForm() {
    const container = document.getElementById(`cta-${formUid}`)
    if (!container) return

    const script = document.createElement("script")
    script.async = true
    script.src = `https://understandlegacycode.ck.page/${formUid}/index.js`
    script.setAttribute("data-uid", formUid)
    container.appendChild(script)
  }

  // Load the form when the page is ready
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", loadCTAForm)
  } else {
    loadCTAForm()
  }
</script>

<style is:global>
  .regular-form {
    @media (min-width: 1100px) {
      margin: auto;
      width: 740px;
    }
  }

  .tilted-form :global(.formkit-form) {
    transform: rotate(-1.5deg);
  }

  .tilted-form :global([data-style="minimal"]) {
    transform: rotate(1.5deg);
    padding: 20px 40px !important;
  }

  .tilted-form :global(.formkit-modal) {
    transform: rotate(1.5deg);
  }

  .tilted-form :global(.formkit-background) {
    opacity: 0.25;
  }

  .tilted-form :global(.formkit-header h1),
  .tilted-form :global(.formkit-subheader p) {
    color: black !important;
  }
</style>
