@use "./mixins" as *;

.button {
  display: inline-flex;
  position: relative;
  justify-content: center;
  align-items: center;
  transition: all var(--animation-speed-fast) var(--cubic-bezier);
  cursor: pointer;
  border: 3px solid light-dark(var(--color-primary-200), var(--color-primary-100));
  border-radius: var(--radius-s);
  background-color: light-dark(var(--color-primary-200), var(--color-primary-100));
  padding: var(--space-xs) var(--space-s);
  inline-size: fit-content;
  color: var(--color-neutral-900);
  font-weight: 700;

  @include text-decoration(transparent, currentColor);

  &:where(:hover, :focus-visible) {
    border-color: light-dark(var(--color-primary-300), var(--color-primary-300));
    background-color: light-dark(var(--color-primary-300), var(--color-primary-300));
    text-decoration-thickness: 2px;
  }

  &.color-secondary {
    border-color: light-dark(var(--color-secondary-100), var(--color-secondary-100));
    background-color: light-dark(var(--color-secondary-100), var(--color-secondary-100));

    &:where(:hover, :focus-visible) {
      border-color: light-dark(var(--color-secondary-300), var(--color-secondary-300));
      background-color: light-dark(var(--color-secondary-300), var(--color-secondary-300));
    }
  }

  &.has-icon {
    display: flex;
    align-items: center;
    gap: var(--space-2xs);

    [data-icon] {
      inline-size: 30px;
      block-size: auto;
    }

    &:where(:hover, :focus-visible) {
      [data-icon] {
        animation: boop 0.5s cubic-bezier(0.165, 0.84, 0.44, 1) forwards;
      }
    }
  }
}
