kbd {
  position: relative;
  inset-block-start: -1px;
  box-shadow: 0 2px 0 1px light-dark(var(--color-neutral-400), var(--color-neutral-700));
  border: 1px solid light-dark(var(--color-neutral-400), var(--color-neutral-600));
  border-radius: var(--radius-s);
  background-color: light-dark(var(--color-neutral-200), var(--color-neutral-800));
  padding: var(--space-4xs) var(--space-3xs);
  aspect-ratio: var(--ratio-square);
  min-inline-size: 12px;
  color: light-dark(var(--color-neutral-900), var(--color-neutral-100));
  font-size: var(--font-size--2);
  font-family: var(--font-family-sans-serif);

  &.flat {
    box-shadow: none;
  }
}
