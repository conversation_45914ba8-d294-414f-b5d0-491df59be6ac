@use "breakpoint" as *;

.container {
  margin-inline: auto;
  padding-inline: var(--grid-gutter);
  max-width: var(--grid-max-width);

  @include breakpoint(m) {
    padding-inline: 2rem;
  }

  @include breakpoint(xl) {
    padding-inline: calc(2rem / 2);
    max-width: 1200px;
  }

  &.stretch {
    max-width: 100%;
  }

  &.narrow {
    max-width: 800px;
  }
}

.center-content {
  display: grid;
  place-items: center;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.horizontal-center {
  margin: 0 auto;
}

.h-stack {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  gap: var(--space-s);
}

.v-stack {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--space-s);
}

.space-between {
  justify-content: space-between;
}

.align-start {
  align-items: flex-start;
}

.align-stretch {
  align-items: stretch;
}

.align-center {
  align-items: center;
}

.align-end {
  align-items: flex-end;
}

.wrap {
  flex-wrap: wrap;
}

.no-gap {
  gap: 0;
}

.truncate {
  overflow: clip;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis {
  display: -webkit-box;
  -webkit-line-clamp: var(--line-clamp);
  -webkit-box-orient: vertical;
  overflow: clip;
}

.uppercase {
  text-transform: uppercase;
}

.sr-only {
  position: absolute;
  overflow: clip;
  clip: rect(0 0 0 0);
  margin: 0;
  border: 0;
  padding: 0;
  inline-size: 1px;
  block-size: auto;
  white-space: nowrap;
}

$space-map: (
  3xs: var(--space-3xs),
  2xs: var(--space-2xs),
  xs: var(--space-xs),
  s: var(--space-s),
  m: var(--space-m),
  l: var(--space-l),
  xl: var(--space-xl),
);

@each $size, $value in $space-map {
  .space-content-#{$size} > * + * {
    margin-block-start: $value;
  }
}

$gap-map: (
  3xs: var(--space-3xs),
  2xs: var(--space-2xs),
  xs: var(--space-xs),
  s: var(--space-s),
  m: var(--space-m),
  l: var(--space-l),
  xl: var(--space-xl),
);

@each $size, $value in $space-map {
  .gap-#{$size} {
    gap: $value;
  }
}

$font-map: (
  -2: (
      font-size: var(--font-size--2),
      line-height: var(--font-size--2),
    ),
  -1: (
      font-size: var(--font-size--1),
      line-height: var(--font-size--1),
    ),
  0: (
    font-size: var(--font-size-0),
    line-height: var(--font-size-0),
  ),
  1: (
    font-size: var(--font-size-1),
    line-height: var(--font-size-1),
  ),
  2: (
    font-size: var(--font-size-2),
    line-height: var(--font-size-2),
  ),
  3: (
    font-size: var(--font-size-3),
    line-height: var(--font-size-3),
  ),
  4: (
    font-size: var(--font-size-4),
    line-height: var(--font-size-4),
  ),
  5: (
    font-size: var(--font-size-5),
    line-height: var(--font-size-5),
  ),
  6: (
    font-size: var(--font-size-6),
    line-height: var(--font-size-6),
  )
);

@each $size, $value in $font-map {
  .font-#{$size} {
    font-size: map-get($value, font-size);
    line-height: map-get($value, line-height);
  }
}

.space-content {
  > * + *,
  > dl > * + * {
    margin-block-start: var(--space-s);
    margin-block-end: 0;
  }

  > h2 {
    margin-block-start: var(--space-l);

    @include breakpoint(xl) {
      margin-block-start: var(--space-xl);
    }
  }

  > h3 {
    margin-block-start: var(--space-m);

    @include breakpoint(xl) {
      margin-block-start: var(--space-l);
    }
  }

  > h4 {
    margin-block-start: var(--space-s);

    @include breakpoint(xl) {
      margin-block-start: var(--space-m);
    }
  }

  > h5,
  > h6 {
    margin-block-start: var(--space-xs);

    @include breakpoint(xl) {
      margin-block-start: var(--space-s);
    }
  }

  > *:first-child {
    margin-block-start: 0;
  }
}
