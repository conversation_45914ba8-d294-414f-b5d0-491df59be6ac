---
name: "prepare-release"

on:
  push:
    branches:
      - "main"
  workflow_dispatch:

jobs:
  pre-release:
    name: "Prepare Release"
    runs-on: "ubuntu-latest"

    steps:
      - uses: "marvinpinto/action-automatic-releases@latest"
        with:
          repo_token: "${{ secrets.GITHUB_TOKEN }}"
          automatic_release_tag: "latest"
          prerelease: true
          title: "Next Release"
